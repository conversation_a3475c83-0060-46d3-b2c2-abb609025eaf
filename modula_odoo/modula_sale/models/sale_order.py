# -*- coding: utf-8 -*-
import base64
import json
from collections import defaultdict
from datetime import datetime, time, timedelta

import magic
from odoo import Command, _, api, fields, models
from odoo.exceptions import UserError, ValidationError
from odoo.http import request
from odoo.tools import (
    SQL,
    float_compare,
    float_is_zero,
    float_round,
    formatLang,
    is_html_empty,
)
from odoo.tools.mail import html_keep_url
from pytz import UTC, timezone

PAYMENT_STATE_SELECTION = [
    ("not_paid", "Not Paid"),
    ("in_payment", "In Payment"),
    ("paid", "Paid"),
    ("partial", "Partially Paid"),
    ("reversed", "Reversed"),
    ("invoicing_legacy", "Invoicing App Legacy"),
]


class SaleOrder(models.Model):
    _inherit = "sale.order"

    need_approve = fields.Boolean(
        string="Is Approved Over Discount Limit Lines",
        compute="_compute_need_approve",
        readonly=False,
        store=True,
    )

    @api.depends("order_line.over_discount_limit")
    def _compute_need_approve(self):
        self = self.with_context(process_need_approve=True)
        for order in self:
            need_approve = any(
                line.over_discount_limit
                and line.discount
                and line.discount_limit_approved != line.discount
                for line in order.order_line
            )
            order.need_approve = need_approve

    def _inverse_need_approve(self):
        for order in self:
            if not order.need_approve:
                order.order_line.filtered(lambda line: line.over_discount_limit).write(
                    {"over_discount_limit": False}
                )

    def check_session_owner_is_store_manager(self):
        employee_id = request.session.get("manager_approve", False)
        if not employee_id:
            return False
        employee_id = self.env["hr.employee"].browse(employee_id)
        store_manager = self.env.ref(
            "modula_sale.store_manager_job", raise_if_not_found=False
        )
        if employee_id.job_id == store_manager:
            return True
        else:
            return False

    def need_employee_selection(self, **kwargs):
        if kwargs.get("order_line"):
            need_approve = []
            sol_env = self.env["sale.order.line"]
            if type(kwargs.get("order_line")) == list:
                order_lines = kwargs.get("order_line")
            else:
                order_lines = [kwargs.get("order_line")]
            return self.is_exist_line_need_approve(order_lines)
        else:
            res = super().need_employee_selection(**kwargs)
            return res

    def is_exist_line_need_approve(self, order_lines):
        need_approve = []
        sale_order_line = self.env["sale.order.line"]
        for order_line in order_lines:
            if isinstance(order_line, list):
                # If remove order line, skip
                if order_line[0] == 2:
                    continue
                # If add order line, check if it is need approve
                if order_line[0] == 1:
                    sale_order_line = sale_order_line.browse(order_line[1])

                if order_line[2].get("product_id"):
                    product_id = order_line[2].get("product_id")
                else:
                    product_id = sale_order_line.product_id.id
                discount = order_line[2].get("discount")
            else:
                product_id = order_line.get("product_id")
                discount = order_line.get("discount")
                sale_order_line = sale_order_line.browse(order_line.get("id"))

            session_owner_employee_id = request.session.get("session_owner", False)
            if session_owner_employee_id and product_id:
                res = sale_order_line.is_this_line_need_approve(
                    product_id, session_owner_employee_id, discount
                )
                if res:
                    need_approve.append(True)
                else:
                    need_approve.append(False)
        return any(need_approve)

    # def _default_checklist_line_ids(self):
    #     added_checklist_question_ids = self.checklist_line_ids.mapped("question_id").ids
    #     domain = [("is_checklist", "=", True)]
    #     question_ids = self.env["survey.question"].search(domain)
    #     write_values = {
    #         "checklist_line_ids": [],
    #     }
    #     for line in question_ids:
    #         question_id = line.id
    #         # Prepare value
    #         line_vals = {"sequence": line.sequence, "question_id": question_id}
    #         if question_id not in added_checklist_question_ids:
    #             write_values["checklist_line_ids"].append((0, 0, line_vals))
    #     return write_values["checklist_line_ids"]

    name = fields.Char(tracking=True)

    state_tab = fields.Selection(
        [
            ("customer", "Customer"),
            ("checklist", "Checklist"),
            ("products", "Products"),
        ],
        string="Sheet",
        copy=False,
        default="customer",
    )
    is_retail_sales = fields.Boolean("Is Retail Sales")
    sale_type = fields.Selection(
        [
            ("retail", "Retail"),
            ("inter-com", "InterCompany"),
        ],
        string="Sale Type",
        compute="_compute_sale_type",
        inverse="_inverse_sale_type",
        store=True,
        required=True,
        precompute=True,
    )
    partner_id = fields.Many2one(domain=[("customer", "=", True)])
    parent_partner_id = fields.Many2one(related="partner_id.parent_id", readonly=False)
    is_company_partner = fields.Boolean(related="partner_id.is_company", readonly=False)
    terms_conditions_ids = fields.Many2many(
        "terms.conditions",
        "sale_terms_conditions_rel",
        "sale_id",
        "terms_conditions_id",
        string="Terms & Conditions",
        compute="_compute_terms_conditions_ids",
        store=True,
    )

    # customer form
    first_name_partner = fields.Char(
        related="partner_id.firstname", readonly=False, store=True
    )
    middle_name_partner = fields.Char(
        related="partner_id.middlename", readonly=False, store=True
    )
    last_name_partner = fields.Char(
        related="partner_id.lastname", readonly=False, store=True
    )
    email_partner = fields.Char(related="partner_id.email", readonly=False, store=True)
    company_partner = fields.Char(
        related="partner_id.company_name", readonly=False, store=True
    )
    work_partner = fields.Char()
    mobile_partner = fields.Char(
        related="partner_id.mobile", readonly=False, store=True
    )
    phone_partner = fields.Char(related="partner_id.phone", readonly=False, store=True)

    unit_number_install = fields.Char(
        related="partner_id.unit_number_install", readonly=False, store=True
    )
    street_number_install = fields.Char(
        related="partner_id.street_number", readonly=False, store=True
    )
    street_install = fields.Char(
        related="partner_id.street_only", readonly=False, store=True
    )
    suburb_install = fields.Char(related="partner_id.city", readonly=False, store=True)
    postcode_install = fields.Char(related="partner_id.zip", readonly=False, store=True)
    state_install = fields.Many2one(
        comodel_name="res.country.state",
        string="State",
        related="partner_id.state_id",
        readonly=False,
        store=True,
    )
    unit_number_billing = fields.Char(
        related="partner_id.unit_number_billing", readonly=False, store=True
    )
    street_number_billing = fields.Char(
        related="partner_id.street_number_billing", readonly=False, store=True
    )
    street_billing = fields.Char(
        related="partner_id.street2_billing", readonly=False, store=True
    )
    suburb_billing = fields.Char(
        related="partner_id.city_billing", readonly=False, store=True
    )
    postcode_billing = fields.Char(
        related="partner_id.zip_billing", readonly=False, store=True
    )
    state_billing = fields.Many2one(
        comodel_name="res.country.state",
        string="State Billing",
        related="partner_id.state_billing_id",
        readonly=False,
        store=True,
    )
    different_billing_address = fields.Boolean(
        string="Different Billing Address?",
        related="partner_id.different_billing_address",
        readonly=False,
        store=True,
    )
    country_id = fields.Many2one(
        string="Country",
        comodel_name="res.country",
    )
    checklist_line_ids = fields.One2many(
        "checklist.checklist",
        "sale_id",
        # compute="_compute_checklist_line_ids",
        # store=True,
        # default=_default_checklist_line_ids,
        copy=True,
    )
    checklist_input_html = fields.Html(
        string="Checklist",
        translate=True,
        sanitize=False,
        sanitize_tags=False,
        sanitize_attributes=False,
        compute="_compute_checklist_input_html",
        # store=True,
        copy=True,
        readonly="locked == True",
    )
    checklist_input_text = fields.Text(string="Checklist Text", copy=True)

    awaiting_instructions_reason = fields.Char(string="Awaiting Instructions Reason")
    plan_numbers = fields.Char(string="Plan Numbers")

    # form Notes
    note_detail = fields.Text("Notes")

    # form Products
    product_retail_id = fields.Many2one("product.template", "Product")
    product_variant_retail_id = fields.Many2one(
        "product.product", "Product", related="product_retail_id.product_variant_id"
    )
    display_qty_widget = fields.Boolean(compute="_compute_display_qty_widget")
    product_uom = fields.Many2one(
        comodel_name="uom.uom",
        string="Unit of Measure",
        related="product_retail_id.uom_id",
    )
    virtual_available = fields.Float(
        "Forecasted Quantity",
        related="product_retail_id.virtual_available",
        digits="Product Unit of Measure",
    )
    free_qty = fields.Float(
        "Free To Use Quantity ",
        digits="Product Unit of Measure",
        related="product_retail_id.free_qty",
    )
    sqmeters = fields.Float("Sq. Meters", compute="_compute_sqmeters", store=True)
    meters = fields.Float(
        "Meters",
    )
    price_total_retail = fields.Monetary(
        "Total Price", compute="_compute_price_total_retail"
    )
    order_line_charges = fields.One2many(
        comodel_name="sale.order.line",
        inverse_name="order_id",
        string="Order Line Charges",
        domain=[("is_charge", "=", True), ("is_retail", "=", True)],
        auto_join=True,
    )
    price_amount_total = fields.Monetary(
        "Amount Total Price", compute="_compute_price_amount_total", store=True
    )
    can_see_review_buttons = fields.Boolean(compute="_compute_can_see_review_buttons")
    image_product = fields.Binary("Image")
    employee_id = fields.Many2one("hr.employee", "Employee")
    uom_retails = fields.Char("UOM Retails")
    confirm_expected = fields.Boolean(default=False)

    # Builder Sale Orders
    retail_location_id = fields.Many2one(
        "stock.location",
        "Retail Source Location",
    )
    add_mailling_list = fields.Boolean(string="Add to mailling list?")
    margin_string = fields.Char("Floor Margin Label", compute="_compute_margin_string")
    margin_floor_string = fields.Char(
        "Margin Label", compute="_compute_margin_floor_string"
    )
    show_is_service_checkbox = fields.Boolean(
        string="Show is service checkbox", compute="_compute_show_is_service_checkbox"
    )
    # show cost price on product table

    # related_orders_ids = fields.Many2many(
    #     "sale.order",
    #     "sale_builders_sale_rel",
    #     "sale_id",
    #     "builders_sale_id",
    #     string="Related Orders",
    #     domain="[('sale_type', '=', 'builder'), ('id', '!=', id)]",
    #     inverse="_inverse_related_orders_ids",
    #     readonly=False,
    # )
    # related_orders_count = fields.Integer(
    #     string="Related Orders Count", compute="_get_related_orders"
    # )
    prepayment_amount = fields.Float(
        string="Prepayment amount",
        help="The amount needed that must be paid by the customer to confirm the order.",
        compute="_compute_prepayment_amount",
        readonly=False,
        store=True,
        copy=False,
    )
    prepayment_percent = fields.Float(
        compute="_compute_prepayment_percent",
        store=True,
        precompute=True,
        copy=False,
    )
    is_retail_payment = fields.Boolean()

    order_line_sum = fields.One2many(
        comodel_name="sale.order.line",
        inverse_name="order_id",
        string="Order Lines Sum",
        auto_join=True,
        domain=[("is_retail", "=", True)],
    )
    order_line_prod = fields.One2many(
        comodel_name="sale.order.line",
        inverse_name="order_id",
        string="Order Lines Sum",
        auto_join=True,
        domain=[("is_charge", "=", False)],
    )
    is_sales_admin = fields.Boolean(
        string="Is Sales Administrator", compute="_compute_is_sales_admin"
    )
    margin_floor = fields.Monetary(
        "Floor Margin", compute="_compute_margin_floor", store=True
    )
    margin_floor_percent = fields.Float(
        "Floor Margin (%)",
        compute="_compute_margin_floor",
        store=True,
        group_operator="avg",
    )
    margin = fields.Monetary()
    margin_percent = fields.Float()
    is_home_approvals = fields.Boolean(
        string="Is Home Approvals (Rent)",
        compute="_compute_is_home_approvals",
        store=True,
    )
    is_state_before_input = fields.Boolean(
        string="Is State Before Input",
        compute="_compute_is_state_before_input",
        store=True,
    )
    location_id = fields.Many2one(
        "stock.location",
        "Source Location",
        compute="_compute_location_id",
        readonly=False,
        store=True,
    )
    is_checklist_mandatory = fields.Boolean(
        string="Is Checklist Mandatory",
        compute="_compute_is_checklist_mandatory",
    )
    is_rewrite = fields.Boolean("Is Rewrite", default=False, copy=False)

    # @api.depends('order_line.price_subtotal', 'order_line.price_tax', 'order_line.price_total')
    # def _compute_amounts(self):
    #     res = super()._compute_amounts()
    #     return res

    def _compute_is_checklist_mandatory(self):
        for rec in self:
            rec.is_checklist_mandatory = False
            if rec.order_line.filtered(
                lambda x: x.product_id
                and x.product_id.type == "service"
                and "pickup" not in x.product_id.name.lower()
                and x.product_id.categ_id
                == self.env.ref("delivery.product_category_deliveries")
            ):
                rec.is_checklist_mandatory = True

    @api.depends("checklist_line_ids")
    def _compute_is_home_approvals(self):
        for rec in self:
            rec.is_home_approvals = False
            if rec.checklist_line_ids:
                checklists = rec.checklist_line_ids.filtered(
                    lambda x: x.question_id and x.question_id.title and x.value
                )
                return_date = checklists.filtered(
                    lambda x: "return date" in x.question_id.title.lower() and x.value
                )
                home_approval = checklists.filtered(
                    lambda x: "rug sale type" in x.question_id.title.lower()
                    and "home approval" in x.value.lower()
                )
                if return_date and home_approval:
                    rec.is_home_approvals = True
                else:
                    rec.is_home_approvals = False

    @api.depends("order_line.margin_floor", "amount_untaxed")
    def _compute_margin_floor(self):
        if not all(self._ids):
            for order in self:
                order.margin_floor = sum(order.order_line.mapped("margin_floor"))
                order.margin_floor_percent = (
                    order.amount_untaxed and order.margin_floor / order.amount_untaxed
                )
        else:
            # On batch records recomputation (e.g. at install), compute the margins
            # with a single read_group query for better performance.
            # This isn't done in an onchange environment because (part of) the data
            # may not be stored in database (new records or unsaved modifications).
            grouped_order_lines_data = self.env["sale.order.line"]._read_group(
                [
                    ("order_id", "in", self.ids),
                ],
                ["order_id"],
                ["margin_floor:sum"],
            )
            mapped_data = {
                order.id: margin_floor
                for order, margin_floor in grouped_order_lines_data
            }
            for order in self:
                order.margin_floor = mapped_data.get(order.id, 0.0)
                order.margin_floor_percent = (
                    order.amount_untaxed and order.margin_floor / order.amount_untaxed
                )

    def _compute_is_sales_admin(self):
        for rec in self:
            rec.is_sales_admin = False
            if self.env.user.has_group(
                "sales_team.group_sale_manager"
            ) or self.env.user.has_group("base.group_system"):
                rec.is_sales_admin = True

    @api.onchange("partner_id")
    def onchange_partner_id_retails(self):
        if self.env.user.has_group(
            "sales_team.group_sale_manager"
        ) or self.env.user.has_group("base.group_system"):
            self.is_sales_admin = True

    @api.depends("warehouse_id.lot_stock_id", "branch_id", "order_line.location_id")
    def _compute_location_id(self):
        def Recursion(location_ids):
            for location_id in location_ids:
                if location_id.branch_id == order.branch_id:
                    return location_id
            if not location_ids.child_ids:
                return False
            return Recursion(location_ids.child_ids)

        for order in self:
            location_id = order.warehouse_id.lot_stock_id
            location_ids = self.env["stock.location"].search(
                [("branch_id", "=", order.branch_id.id)]
            )
            if order.branch_id:
                location_id = Recursion(location_ids)
            order.location_id = location_id
            # for line in order.order_line:
            #     if not line.location_id and order.location_id:
            #         line.location_id = order.location_id.id

    # Round Up to whole dollar in retail sale only
    def _compute_amounts_test(self):
        for order in self:
            if order.sale_type == "retail":
                order.amount_total = round(order.amount_total)

    @api.depends("prepayment_amount", "require_payment", "minimum_deposit_total")
    def _compute_prepayment_percent(self):
        for order in self:
            if (
                order.require_payment
                and order.prepayment_amount > 0.0
                and order.amount_total > 0
            ):
                order.prepayment_percent = order.prepayment_amount / order.amount_total
            else:
                order.prepayment_percent = order.company_id.prepayment_percent

    @api.constrains("prepayment_amount")
    def _check_prepayment_amount(self):
        for order in self:
            if (
                order.prepayment_amount
                and not (order.prepayment_amount <= round(order.remaining_amount, 2))
                and float_compare(order.remaining_amount, 0, 2) > 0
            ):
                raise ValidationError(_("Prepayment amount must be a valid amount."))

    @api.depends(
        "amount_total", "minimum_deposit_total", "remaining_amount", "downpayment_ids"
    )
    def _compute_prepayment_amount(self):
        for order in self:
            order.prepayment_amount = 0.0
            if order.amount_total > 0:
                remaining_downpayment = order.remaining_amount
                expected_first = round(order.minimum_deposit_total, 2)
                remaining = round(remaining_downpayment, 2)

                if float_compare(remaining, expected_first, 2) >= 0:
                    order.prepayment_amount = expected_first
                else:
                    order.prepayment_amount = remaining

    def _get_prepayment_required_amount(self):
        """Return the minimum amount needed to confirm automatically the quotation.

        Note: self.ensure_one()

        :return: The minimum amount needed to confirm automatically the quotation.
        :rtype: float
        """
        amount = super()._get_prepayment_required_amount()
        if self.require_payment and self.prepayment_amount > 0.0:
            return self.prepayment_amount
        return amount

    # @api.depends("related_orders_ids")
    # def _get_related_orders(self):
    #     for order in self:
    #         order.related_orders_count = len(order.related_orders_ids)

    def _inverse_related_orders_ids(self):
        """Inverse method to link the orders back to each other."""
        for rec in self:
            # Get current related orders
            current_related_orders = rec.related_orders_ids.ids

            # Handle new related orders: Add reciprocal links
            for order in rec.related_orders_ids:
                if rec.id not in order.related_orders_ids.ids:
                    order.related_orders_ids = [(4, rec.id)]

            # Handle removed related orders: Remove reciprocal links
            related_orders = self.search([("sale_type", "=", "builder")])
            related_orders = related_orders.filtered(
                lambda r: r.related_orders_ids and rec.id in r.related_orders_ids.ids
            )
            for order in related_orders:
                if order.id not in current_related_orders:
                    order.related_orders_ids = [(3, rec.id)]

    @api.depends("checklist_line_ids")
    def _compute_show_is_service_checkbox(self):
        for rec in self:
            rec.show_is_service_checkbox = False
            if rec.checklist_line_ids:
                checklists = rec.checklist_line_ids.filtered(
                    lambda x: x.question_id and x.question_id.title and x.value
                )
                if checklists.filtered(
                    lambda x: "over-locking" in x.question_id.title.lower()
                    and "yes" in x.value.lower()
                ):
                    rec.show_is_service_checkbox = True
                else:
                    rec.show_is_service_checkbox = False

    @api.depends("margin_floor", "margin_floor_percent")
    def _compute_margin_floor_string(self):
        for rec in self:
            currency = rec.currency_id.symbol or ""
            margin_str = "{:,.2f}".format(rec.margin_floor)
            margin_percent_str = "{:.2f}".format(rec.margin_floor_percent * 100)

            if rec.margin_floor_percent <= 0.0:
                rec.margin_floor_string = f"{currency}{margin_str}"
            else:
                rec.margin_floor_string = (
                    f"{currency}{margin_str} ({margin_percent_str}%)"
                )

    @api.depends("margin", "margin_percent")
    def _compute_margin_string(self):
        for rec in self:
            currency = rec.currency_id.symbol or ""
            margin_str = "{:,.2f}".format(rec.margin)
            margin_percent_str = "{:.2f}".format(rec.margin_percent * 100)

            if rec.margin_percent <= 0.0:
                rec.margin_string = f"{currency}{margin_str}"
            else:
                rec.margin_string = f"{currency}{margin_str} ({margin_percent_str}%)"

    def get_install_service_product_domain(self):
        return [
            "|",
            "|",
            "|",
            ("default_code", "=ilike", "sfi"),
            ("default_code", "=ilike", "hfi"),
            ("default_code", "=ilike", "sfi service"),
            ("default_code", "=ilike", "hfi service"),
        ]

    @api.depends("state")
    def _compute_is_state_before_input(self):
        for rec in self:
            rec.is_state_before_input = rec.state in ("draft", "sent")

    def write(self, vals):
        # Check if trying to update checklist fields on a locked order
        checklist_fields = [
            "checklist_line_ids",
            "checklist_input_html",
            "checklist_input_text",
        ]
        if any(field in vals for field in checklist_fields):
            for record in self:
                if record._is_readonly():
                    raise UserError(
                        _(
                            "You cannot modify checklist data on a locked sale order. Please unlock it first."
                        )
                    )

        if vals.get("parent_partner_id"):
            vals["company_partner"] = False
        need_approve = False
        if vals.get("order_line"):
            need_approve = self.is_exist_line_need_approve(vals.get("order_line"))
        if need_approve:
            vals = self.process_need_approve(vals)
        res = super().write(vals)

        # rounding up the amount by creating a new sale line
        if not self.env.context.get("is_come_from_retail_app"):
            return res
        return res

    def process_need_approve(self, vals):
        if self.check_session_owner_is_store_manager():
            self = self.with_context(pass_approve=True)
            vals.update(
                {
                    "need_approve": False,
                }
            )
            if self.env.context.get("create_order"):
                if vals.get("order_line"):
                    vals = self.update_order_line_values_manager_approve(vals)
            else:
                if vals.get("order_line"):
                    vals = self.update_order_line_values_manager_approve(vals)

            request.session["manager_approve"] = False
        else:
            raise ValidationError(
                "Discount is exceeds your allowance, please request Manager approval."
            )
        return vals

    def update_order_line_values_manager_approve(self, vals):
        if vals.get("order_line"):
            for val in vals.get("order_line"):
                new_vals = val[2]
                new_vals.update(
                    {
                        "discount_limit_approved": new_vals.get("discount"),
                        "over_discount_limit": True,
                        "employee_approved_discount": request.session.get(
                            "manager_approve", False
                        ),
                    }
                )
                val[2] = new_vals
        return vals

    def _check_checklist_mandatory_sale(self, state):
        self.ensure_one()
        if state != "draft":
            self.load_template()
            question_ids = self.checklist_line_ids.filtered(
                lambda x: x.question_id
                and x.question_id.constr_mandatory
                and not x.value
            ).mapped("question_id")
            if question_ids:
                question_names = ", ".join(
                    question_ids.mapped(lambda q: q.name if q.name else q.title)
                )
                raise ValidationError(
                    _(
                        "Please fill in the required questions before proceeding: %(question_names)s",
                        question_names=question_names,
                    )
                )

    def _check_checklist_mandatory(self, state_tab):
        self.ensure_one()
        if state_tab in ("products", "photos", "document", "detail_sale"):
            self.load_template()
            question_ids = self.checklist_line_ids.filtered(
                lambda x: x.question_id
                and x.question_id.constr_mandatory
                and not x.value
            ).mapped("question_id")
            if question_ids:
                question_names = ", ".join(
                    question_ids.mapped(lambda q: q.name if q.name else q.title)
                )
                raise ValidationError(
                    _(
                        "Please fill in the required questions before proceeding: %(question_names)s",
                        question_names=question_names,
                    )
                )

    @api.onchange("state", "state_tab")
    def _onchange_state_tab(self):
        # self._check_checklist_mandatory(self.state_tab)
        self._check_checklist_mandatory_sale(self.state)

    def load_template(self, is_ecommerce=False):
        self.ensure_one()
        self = self.sudo()
        added_checklist_question_ids = self.checklist_line_ids.mapped("question_id").ids
        domain = [("is_checklist", "=", True)]
        # if is_ecommerce:
        #     domain.append(("is_checklist_ecommerce", "=", True))
        question_ids = (
            self.env["survey.question"].sudo().search(domain, order="sequence")
        )
        for line in question_ids:
            question_id = line.id
            # Prepare value
            line_vals = {"sequence": line.sequence, "question_id": question_id}
            write_values = {
                "checklist_line_ids": [],
            }
            if question_id not in added_checklist_question_ids:
                write_values["checklist_line_ids"].append((0, 0, line_vals))
            self.with_context(load_template=True).write(write_values)

    # @api.depends("checklist_input_text")
    def _compute_checklist_line_ids(self):
        for rec in self:
            # rec.checklist_line_ids.write({"value": ""})  # Clear all values initially
            if rec.checklist_input_text:
                question_answers = {}
                answers_ids = []
                list_qa = rec.checklist_input_text.split(",")
                for qa in list_qa:
                    qa_parts = qa.split("_")
                    if len(qa_parts) == 2:
                        question_id, answer_id = int(qa_parts[0]), int(qa_parts[1])
                        answers_ids.append(answer_id)
                        if question_id not in question_answers:
                            question_answers[question_id] = []
                        question_answers[question_id].append(answer_id)
                    elif len(qa_parts) == 3:
                        question_id, answer_id, value = (
                            int(qa_parts[0]),
                            int(qa_parts[1]),
                            qa_parts[2],
                        )
                        answers_ids.append(answer_id)
                        if question_id not in question_answers:
                            question_answers[question_id] = []
                        question_answers[question_id].append(value)
                for question_id, answer_ids in question_answers.items():
                    line = rec.checklist_line_ids.filtered(
                        lambda x: x.question_id.id == question_id
                    )
                    # Compute the new values from answers that are not in triggering answers list
                    triggering_ids = [
                        ans.id
                        for ans in line.question_id.triggering_answer_checklist_ids
                    ]
                    valid_answers = [
                        answer.value
                        for answer in line.question_id.suggested_answer_ids
                        if answer.id in answer_ids and answer.id not in triggering_ids
                    ]

                    if valid_answers:
                        line.value = ", ".join(valid_answers)
                    else:
                        line.value = (
                            question_answers[question_id]
                            and question_answers[question_id][0]
                            or ""
                        )  # Clear value if no valid answers or all are triggering answers

                # Additional loop to clear values if the answer is in triggering_answer_checklist_ids
                for line in rec.checklist_line_ids:
                    for answer in line.question_id.triggering_answer_checklist_ids:
                        if answer.id not in answers_ids:
                            line.value = ""

    # @api.depends("state_tab", "checklist_line_ids")
    def _compute_checklist_input_html(self):
        for rec in self:
            # Check if the sale order is locked or readonly
            is_readonly = rec._is_readonly()
            if is_readonly:
                # If locked, don't allow editing - just display the current values
                input = rec.compute_html_template(rec.checklist_line_ids, readonly=True)
                rec.checklist_input_html = input
            else:
                # Normal flow when not locked
                rec.load_template()
                # Render the tables
                rec._compute_checklist_line_ids()
                input = rec.compute_html_template(
                    rec.checklist_line_ids, readonly=False
                )
                rec.checklist_input_html = input

    def compute_html_template(self, source_input_lines, readonly=False):
        """
        Function used to generate the html view for Checklist
        - Input:
            + source_input_lines: input lines
            + readonly: whether the form should be readonly (for locked orders)
        """
        self.ensure_one()
        input_content = ""
        source_input_lines = source_input_lines.search(
            [("id", "in", source_input_lines.ids)], order="sequence"
        )
        for line in source_input_lines.filtered(
            lambda x: x.question_id.suggested_answer_ids
            or x.question_id.question_type_checklist
            not in ["simple_choice", "multiple_choice"]
        ):
            name_element = ""
            input_title = ""
            tip_to_user = ""
            if line.question_id.question_type_checklist == "multiple_choice":
                tip_to_user = "Tip: You can select multiple options."
            if line.question_id.name:
                # Add a title
                input_title = _(
                    "<span>%(question)s</span>",
                    question=line.question_id.name,
                )
                name_element = line.question_id.name.lower().replace(" ", "_")
            input_answer = ""
            selected_value = ""
            if line.value:
                selected_value = line.value

            # Add readonly attributes if the order is locked
            readonly_attr = (
                "disabled='disabled' style='pointer-events: none; opacity: 0.6;'"
                if readonly
                else ""
            )

            for answer in line.question_id.suggested_answer_ids:
                style = ""
                active = ""
                color = answer.color or "#288c74"

                index = str(line.question_id.id) + "_" + str(answer.id)
                if line.question_id.question_type_checklist == "simple_choice":
                    if selected_value and answer.value == selected_value:
                        style = _(
                            "style='background-color: %(color)s; color: white;border-color: %(color)s;'",
                            color=color,
                        )
                        active = "active"
                    input_answer += _(
                        "<span id='simple_choice_%(id)s' class='custom_selection_badge btn btn-secondary mb-1 %(active)s' data-id='%(index)s' data-color='%(color)s' %(style)s value='%(value)s' %(readonly_attr)s>%(name)s</span>",
                        id=str(answer.id),
                        index=index,
                        active=active,
                        color=answer.color,
                        style=style,
                        value=answer.value.lower().replace(" ", "_"),
                        name=answer.value,
                        readonly_attr=readonly_attr,
                    )
                elif line.question_id.question_type_checklist == "multiple_choice":

                    if selected_value and answer.value in selected_value:
                        style = _(
                            "style='background-color: %(color)s; color: white;border-color: %(color)s;'",
                            color=color,
                        )
                        active = "active"
                    input_answer += _(
                        "<span id='multiple_choice_%(id)s' class='custom_selection_badge btn btn-secondary mb-1 %(active)s' data-id='%(index)s' data-color='%(color)s' %(style)s value='%(value)s' %(readonly_attr)s>%(name)s</span>",
                        id=str(answer.id),
                        index=index,
                        active=active,
                        color=answer.color,
                        style=style,
                        value=answer.value.lower().replace(" ", "_"),
                        name=answer.value,
                        readonly_attr=readonly_attr,
                    )
            text_box = ""
            if line.question_id.question_type_checklist == "text_box":
                text_box = "text_box"
                index = str(line.question_id.id) + "_" + "0"
                input_answer += _(
                    "<textarea rows='2' id='text_box_%(id)s' class='custom_selection_badge form-control background_text_box' data-id='%(index)s' %(readonly_attr)s>%(selected_value)s</textarea>",
                    id=str(line.question_id.id),
                    index=index,
                    selected_value=selected_value,
                    readonly_attr=readonly_attr,
                )
            elif line.question_id.question_type_checklist == "char_box":
                text_box = "text_box"
                index = str(line.question_id.id) + "_" + "0"
                input_answer += _(
                    "<input type='text' id='text_box_%(id)s' class='custom_selection_badge form-control background_text_box' data-id='%(index)s' value='%(selected_value)s' %(readonly_attr)s></input>",
                    id=str(line.question_id.id),
                    index=index,
                    selected_value=selected_value,
                    readonly_attr=readonly_attr,
                )
            elif line.question_id.question_type_checklist == "date":
                text_box = "date_box"
                style = ""
                if selected_value:
                    style = _(
                        "style='background-color: %(color)s; color: white;border-color: %(color)s;font-weight: 500;border-radius: 0;'",
                        color="#006400",
                    )
                    active = "active"
                index = str(line.question_id.id) + "_" + "0"
                input_answer += _(
                    "<input type='date' id='date_box_%(id)s' class='custom_selection_badge form-control' data-id='%(index)s' %(style)s value='%(selected_value)s' %(readonly_attr)s></input>",
                    id=str(line.question_id.id),
                    index=index,
                    style=style,
                    selected_value=selected_value,
                    readonly_attr=readonly_attr,
                )
            elif line.question_id.question_type_checklist == "datetime":
                text_box = "datetime_box"
                index = str(line.question_id.id) + "_" + "0"
                input_answer += _(
                    "<input type='datetime-local' id='datetime_box_%(id)s' class='form-control' data-id='%(index)s' %(readonly_attr)s></input>",
                    id=str(line.question_id.id),
                    index=index,
                    readonly_attr=readonly_attr,
                )
            hide_lst = []
            style_hide = ""
            if line.question_id.triggering_answer_checklist_ids:
                for hide_answer in line.question_id.triggering_answer_checklist_ids:
                    hide_lst.append(hide_answer.id)
                    triggering_answer = source_input_lines.filtered(
                        lambda x: x.question_id == hide_answer.question_id
                    ).mapped("value")
                    if hide_answer.value in triggering_answer:
                        continue
                    style_hide = "style='display: none;'"
            hide_str = hide_lst and "_".join(map(str, hide_lst)) or ""
            if text_box == "text_box":
                input_content += _(
                    """
                    <div %(style_hide)s data-hide='%(hide_str)s' title='%(tip_to_user)s'>
                        %(input_title)s
                        <div class='d-flex field_checklist %(text_box)s'>
                            <label class='o_form_label' for='%(name_element)s'>%(question)s</label>
                            <div name='%(name_element)s' class='o_field_widget o_field_selection_badge'>
                                <div class='d-flex flex-wrap gap-1 mb-3' style='%(width)s'>%(answers)s</div>
                            </div>
                        </div>
                    </div>
                    """,
                    input_title=input_title,
                    tip_to_user=tip_to_user,
                    text_box=text_box,
                    style_hide=style_hide,
                    hide_str=hide_str,
                    question=line.question_id.title,
                    name_element=name_element,
                    width="width: 100%;",
                    answers=input_answer,
                )
            else:
                input_content += _(
                    """
                    <div %(style_hide)s data-hide='%(hide_str)s' title='%(tip_to_user)s'>
                        %(input_title)s
                        <div class='d-flex field_checklist %(text_box)s'>
                            <label class='o_form_label' for='%(name_element)s'>%(question)s</label>
                            <div name='%(name_element)s' class='o_field_widget o_field_selection_badge'>
                                <div class='d-flex flex-wrap gap-1 mb-3'>%(answers)s</div>
                            </div>
                        </div>
                    </div>
                    """,
                    input_title=input_title,
                    tip_to_user=tip_to_user,
                    text_box=text_box,
                    style_hide=style_hide,
                    hide_str=hide_str,
                    question=line.question_id.title,
                    name_element=name_element,
                    answers=input_answer,
                )
        return input_content

    @api.depends("product_variant_retail_id")
    def _compute_display_qty_widget(self):
        """Compute the visibility of the inventory widget."""
        for line in self:
            if (
                line.product_variant_retail_id.type == "consu"
                and line.product_variant_retail_id.is_storable
                and line.product_uom
            ):
                line.display_qty_widget = True
            else:
                line.display_qty_widget = False

    @api.depends("checklist_line_ids", "partner_id")
    def _compute_terms_conditions_ids(self):
        for rec in self:
            terms_conditions = []
            if rec.checklist_line_ids:
                checklists = rec.checklist_line_ids.filtered(
                    lambda x: x.question_id and x.question_id.title and x.value
                )
                if checklists.filtered(
                    lambda x: "rug sale" in x.question_id.title.lower()
                    and "invoice" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_invoice",
                            raise_if_not_found=False,
                        ).id
                    )
                if checklists.filtered(
                    lambda x: "rug sale" in x.question_id.title.lower()
                    and "lay-by" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_layby",
                            raise_if_not_found=False,
                        ).id
                    )
                if checklists.filtered(
                    lambda x: "rug sale" in x.question_id.title.lower()
                    and "approval" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_home_approval",
                            raise_if_not_found=False,
                        ).id
                    )
                if checklists.filtered(
                    lambda x: "clearance stock" in x.question_id.title.lower()
                    and "yes" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_disclaimer_clearance_stock",
                            raise_if_not_found=False,
                        ).id
                    )
                if checklists.filtered(
                    lambda x: "runners" in x.question_id.title.lower()
                    and "yes" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_disclaimer_runners",
                            raise_if_not_found=False,
                        ).id
                    )
                if checklists.filtered(
                    lambda x: "over-locking" in x.question_id.title.lower()
                    and "yes" in x.value.lower()
                ):
                    terms_conditions.append(
                        rec.env.ref(
                            "modula_sale.terms_conditions_disclaimer_overlocking",
                            raise_if_not_found=False,
                        ).id
                    )
            rec.terms_conditions_ids = [(6, 0, terms_conditions)]
            if not rec.terms_conditions_ids:
                terms_conditions_ids = (
                    rec.env["terms.conditions"]
                    .sudo()
                    .search([("name", "=", "Terms & Conditions")], limit=1)
                )
                if terms_conditions_ids:
                    rec.terms_conditions_ids = [(6, 0, terms_conditions_ids.ids)]

    # def get_state(self, current_state, move="next"):
    #     # based on RETAIL and BUILDER list
    #     if not current_state or current_state not in [
    #         state[0] for state in RETAIL_STATE + BUILDER_STATE
    #     ]:
    #         return False
    #     if self.is_retail_sales:
    #         retail_index = [state[0] for state in RETAIL_STATE].index(current_state)
    #         if move == "next":
    #             next_state = RETAIL_STATE[retail_index + 1][0]
    #         if move == "previous":
    #             next_state = RETAIL_STATE[retail_index - 1][0]
    #     else:
    #         builder_index = [state[0] for state in BUILDER_STATE].index(current_state)
    #         if move == "next":
    #             next_state = BUILDER_STATE[builder_index + 1][0]
    #         if move == "previous":
    #             next_state = BUILDER_STATE[builder_index - 1][0]
    #     return next_state

    # def action_process_order(self, force_close=False):
    #     self.ensure_one()
    #     if force_close:
    #         self.write({"state": "complete"})
    #         return

    #     # get the current state of the sale order
    #     current_state = self.state
    #     # get the next state of the sale order
    #     if current_state in ("draft", "sent"):
    #         next_state = "achieved" if self.is_retail_sales else "in_progress"
    #     else:
    #         next_state = self.get_state(current_state, "next")
    #     if not next_state:
    #         return
    #     # call action confirm on the correct state
    #     if next_state == "sale":
    #         if not self.branch_id:
    #             raise UserError(
    #                 _("You must select a branch to validate to Input stage")
    #             )
    #         res = self.action_confirm()
    #         self.locked = True
    #         return res
    #     self.write({"state": next_state})

    # def action_submit(self):
    #     self.write({"state": "achieved"})

    # def action_reject_order(self):
    #     self.ensure_one()
    #     # get the current state of the sale order
    #     current_state = self.state
    #     # get the next state of the sale order
    #     previous_state = self.get_state(current_state, "previous")
    #     if not previous_state:
    #         return
    #     if current_state == "sale":
    #         self.write({"state": previous_state, "locked": False})
    #     else:
    #         self.write({"state": previous_state})

    # def _confirmation_error_message(self):
    #     res = super(SaleOrder, self)._confirmation_error_message()
    #     if res and self.state in ("ops_review", "contact_manager"):
    #         return False
    #     return res

    # @api.depends("state")
    # def _compute_can_see_review_buttons(self):
    #     for rec in self:
    #         state = rec.state
    #         if state in ("draft", "sent"):
    #             rec.can_see_review_buttons = True
    #             continue
    #         if state in ("cancel", "complete", "rejected"):
    #             rec.can_see_review_buttons = False
    #             continue
    #         if state == "sale" and self.env.context.get("is_come_from_builder_app"):
    #             rec.can_see_review_buttons = False
    #             continue
    #         # check the corresponding access right
    #         access_right = self.get_state(state, "next")
    #         if not access_right:
    #             rec.can_see_review_buttons = False
    #             continue
    #         if rec.is_retail_sales:
    #             access_right = "retail_" + access_right
    #         else:
    #             access_right = "builder_" + access_right
    #         # check if user have the access_right field set to True
    #         rec.can_see_review_buttons = self.env.user[access_right]

    @api.depends(
        "product_retail_id.depth", "product_retail_id.width", "meters", "uom_retails"
    )
    def _compute_sqmeters(self):
        for rec in self:
            sqmeters = 0
            product = rec.product_retail_id
            if product and rec.uom_retails:
                if rec.uom_retails.lower() == "lm":
                    sqmeters = product.width * rec.meters / 100
                else:
                    sqmeters = product.width * product.depth * rec.meters / 100
            rec.sqmeters = sqmeters

    @api.onchange("product_retail_id")
    def onchange_product_retail_id(self):
        product = self.product_retail_id
        if product:
            self.image_product = product.image_1920
            self.meters = 1
            self.retail_location_id = self.location_id.id
            self.uom_retails = product.uom_id.name if product.uom_id else "lm"
        else:
            self.meters = 0
            self.price_total_retail = 0
            self.image_product = False
            self.retail_location_id = False

    @api.depends("partner_id.customer_type_id")
    def _compute_sale_type(self):
        retail_partner_type = self.env.ref(
            "modula_contact_extended.res_partner_type_dom", raise_if_not_found=False
        )
        intercom_partner_type = self.env.ref(
            "modula_contact_extended.res_partner_type_inter_com",
            raise_if_not_found=False,
        )
        for rec in self:
            sale_type = "retail"
            if rec.partner_id:
                sale_type = (
                    "retail"
                    if (
                        retail_partner_type
                        and rec.partner_id.customer_type_id == retail_partner_type
                    )
                    else sale_type
                )
                sale_type = (
                    "inter-com"
                    if (
                        intercom_partner_type
                        and rec.partner_id.customer_type_id == intercom_partner_type
                    )
                    else sale_type
                )
            rec.sale_type = sale_type

    def _inverse_sale_type(self):
        for rec in self:
            rec.is_retail_sales = rec.sale_type == "retail"

    @api.depends("sqmeters", "meters")
    def _compute_price_total_retail(self):
        for rec in self:
            product = self.product_retail_id
            price_total_retail = 0.0
            if product:
                price_total_retail = rec.meters * product.list_price
            rec.price_total_retail = price_total_retail

    @api.depends("amount_untaxed")
    def _compute_price_amount_total(self):
        for rec in self:
            rec.price_amount_total = rec.amount_untaxed

    def create_charge_line_default(self, company):
        self.ensure_one()
        charge_line = self.env["default.charges"].search(
            [
                ("answer_id", "=", False),
                ("default_charges_ids", "!=", False),
                ("company_id", "=", company.id),
            ]
        )
        if not charge_line:
            return
        vals_line = []
        for prod in charge_line.mapped("default_charges_ids"):
            product = prod.sudo()
            if not product:
                continue
            last_sequence_line = max(self.order_line.mapped("sequence"), default=0) + 1
            vals_line.append(
                {
                    "name": "{}{}".format(
                        product.default_code and "[%s] " % product.default_code or "",
                        product.name,
                    ),
                    "product_template_id": product.id,
                    "price_unit": prod.list_price,
                    "product_uom_qty": 1,
                    "order_id": self.id,
                    "currency_id": product.currency_id.id,
                    "sequence": last_sequence_line,
                    "product_uom": product.uom_id.id,
                    "product_id": product.product_variant_id.id,
                    "is_retail": True,
                }
            )
        if vals_line:
            self.env["sale.order.line"].sudo().create(vals_line)

    # def _confirmation_error_message(self):
    #     """Return whether order can be confirmed or not if not then returm error message."""
    #     BEFORE_INPUT_STATE = [
    #         "draft",
    #         "sent",
    #         "achieved",
    #         "admin_review",
    #         "carpet_review",
    #         "ops_review",
    #         "in_progress",
    #         "account_manager",
    #         "contact_manager",
    #     ]
    #     self.ensure_one()
    #     if self.state not in BEFORE_INPUT_STATE:
    #         return _("Some orders are not in a state requiring confirmation.")
    #     if any(
    #         not line.display_type and not line.is_downpayment and not line.product_id
    #         for line in self.order_line
    #     ):
    #         return _("A line on these orders missing a product, you cannot confirm it.")

    #     return False

    def _compute_user_id(self):
        for order in self:
            if order.partner_id and not (order._origin.id and order.user_id):
                order.user_id = self.env.user

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get("parent_partner_id"):
                vals["company_partner"] = False
        if self._check_orderline_discount_limit(vals):
            vals = self.with_context(create_order=True).process_need_approve(vals)

        records = super(SaleOrder, self).create(vals_list)
        for rec in records:
            if self._context.get("default_is_retail_sales") or rec.is_retail_sales:
                rec.is_retail_sales = True
                rec = rec.with_context(bypass_employee_selection=True)
                if request.session.get("session_owner", False):
                    rec.employee_id = request.session.get("session_owner", False)
                rec.state = "draft"
                if not rec.order_line_sum:
                    try:
                        rec.create_charge_line_default(rec.company_id)
                    except ValueError:
                        pass

        ##recompute the service note on create
        # for line in records.order_line:
        #     line._onchange_is_subcontract_service()
        return records

    def _check_orderline_discount_limit(self, vals):
        if vals.get("order_line"):
            for val in vals.get("order_line"):
                if val[0] == 5:
                    continue
                order_line_val = val[2]
                product_id = False
                session_owner_employee_id = False
                if order_line_val.get("product_id"):
                    product_id = order_line_val.get("product_id")
                if request.session.get("session_owner", False):
                    session_owner_employee_id = request.session.get(
                        "session_owner", False
                    )
                if session_owner_employee_id and product_id:
                    if order_line_val.get("discount") and order_line_val.get(
                        "discount"
                    ) > self.order_line._get_min_discount_limit_from_product_id_employee_id(
                        product_id, session_owner_employee_id
                    ):
                        return True
        return False

    @api.depends("state")
    def _compute_type_name(self):
        super(SaleOrder, self)._compute_type_name()
        for record in self:
            if record.state in ("draft", "sent", "cancel"):
                record.type_name = _("Sales Order NC")
            elif record.state in ("sale", "locked"):
                record.type_name = _("Confirmed Order")

    def action_preview_invoice_on_sale_order(self):
        self.ensure_one()
        if self.is_retail_sales:
            self.is_retail_payment = True
            return {
                "type": "ir.actions.act_url",
                "target": "self",
                "url": self.get_portal_url(suffix="/retails"),
            }

    def action_confirm(self):
        for order in self:
            if not order.branch_id and not order.website_id:
                raise UserError(
                    _("You must select a branch to proceed with the sales order.")
                )
        return super().action_confirm()

    def action_hold_confirm(self):
        for order in self:
            if not order.branch_id:
                raise UserError(
                    _("You must select a branch to proceed with the sales order.")
                )
        return super().action_hold_confirm()

    def action_preview_sale_order(self):
        if not self.branch_id:
            raise UserError(
                _("You must select a branch to proceed with the sales order.")
            )
        action = super().action_preview_sale_order()
        self.is_retail_payment = False
        return action

    def _get_portal_return_retail_action(self):
        """Return the action used to display orders when returning from customer portal."""
        self.ensure_one()
        return self.env.ref("modula_sale.action_orders_portal")

    def _get_name_portal_content_view(self):
        """This method can be inherited by localizations who want to localize the online quotation view."""
        self.ensure_one()
        if self.is_retail_sales:
            return "modula_sale.sale_order_retails_portal_content"
        else:
            return super()._get_name_portal_content_view()

    def _create_order_line_for_product(
        self,
        product_lines,
        order_line,
        underlay=False,
        delivery="delivery",
        native=False,
    ):
        # ref this later, ask tina
        self.ensure_one()
        vals_list = []
        rooms = ""
        location_id = self.retail_location_id or self.location_id
        last_sequence = max(
            product_lines.filtered(lambda l: l.sequence > 0).mapped("sequence"),
            default=0,
        )
        product = self.product_retail_id
        # if underlay:
        #     product = self.product_retail_id.default_underlay_code_id
        if product.categ_id and product.categ_id.is_charge:
            if not location_id:
                location_id = self.warehouse_id.lot_stock_id or False
        else:
            last_sequence = (
                min(
                    product_lines.filtered(lambda l: l.sequence >= 0).mapped(
                        "sequence"
                    ),
                    default=0,
                )
                - 1
            )
            if underlay:
                last_sequence += 1
        # Create product lines
        last_sequence += 1
        val = {
            "name": "{}{}".format(
                product.default_code and "[%s] " % product.default_code or "",
                product.name,
            ),
            "product_template_id": product.id,
            "price_unit": product.list_price,
            "sqmeters": underlay and product.sq_meters or self.sqmeters,
            "product_uom_qty": self.meters,
            "meters": self.meters,
            "order_id": self.id,
            "currency_id": product.currency_id.id,
            "location_id": location_id and location_id.id or False,
            "delivery_method": delivery,
        }
        # add the sequence only if the sale is not rug
        val["sequence"] = (
            max(
                product_lines.filtered(lambda l: l.sequence > 0).mapped("sequence"),
                default=0,
            )
            + 1
        )
        if native:
            val.pop("meters", None)
            val["product_uom"] = product.uom_id.id
            val["is_retail"] = True
            val["product_id"] = product.product_variant_id.id
            val["delivery_method"] = delivery
        if product.type in ["service", "consu"]:
            product_service_line = self.order_line.filtered(
                lambda l: l.product_template_id.type in ["service", "consu"]
            )
            max_sequence_line = (
                product_service_line
                and product_service_line.sorted(key=lambda r: r.sequence)[-1].sequence
                or 0
            )
            product_main_line = self.order_line.filtered(
                lambda l: l.is_subcontract_service and l.sequence >= max_sequence_line
            )
            if product_main_line:
                max_sequence_line = product_main_line.sorted(key=lambda r: r.sequence)[
                    -1
                ]
                val["delivery_method"] = max_sequence_line.delivery_method
                val["location_id"] = (
                    max_sequence_line.location_id
                    and max_sequence_line.location_id.id
                    or False
                )
                val["route_id"] = (
                    max_sequence_line.route_id
                    and max_sequence_line.route_id.id
                    or False
                )
        vals_list.append(val)
        # Create product lines
        order_line_products = order_line.create(vals_list)
        return order_line_products

    def add_order_line_product(self, underlay=False, delivery=False):
        self.ensure_one()
        order_line = self.env["sale.order.line"].sudo()
        sale_lines = self._create_order_line_for_product(
            self.order_line, order_line, underlay, delivery, native=True
        )
        for line in sale_lines:
            line.is_retail = True

    def add_location_line_and_check_stock_rug(self):
        stock_location = self.env["stock.location"].search([("branch_id", "!=", False)])
        if not stock_location:
            raise UserError(_("No stock locations found for the user's companies."))
        val_line_ids = []
        line_ids = []
        for location in stock_location:
            line_quant_exits = (
                self.env["stock.location.quant.line"]
                .sudo()
                .search([("retail_location_id", "=", location.id)])
            )
            qty_onhand_retail = (
                self.env["stock.quant"]
                .sudo()
                ._get_onhand_quantity_rug(self.product_retail_id, location)
            )
            if line_quant_exits:
                line_quant_exits.write(
                    {
                        "select": False,
                        "qty_onhand_retail": qty_onhand_retail,
                        "sale_id": self.id,
                        "quantity": 0,
                        "delivery_method": False,
                        "restrict_delivery_method": False,
                    }
                )
                remaining_lines = line_quant_exits[1:]
                remaining_lines.unlink()
                if qty_onhand_retail == 0:
                    continue
                line_ids.append(line_quant_exits[0].id)
            elif qty_onhand_retail != 0:
                val_line_ids.append(
                    {
                        "retail_location_id": location.id,
                        "branch_id": location.branch_id.id,
                        "quantity": 0,
                        "qty_onhand_retail": qty_onhand_retail,
                        "sale_id": self.id,
                    }
                )
        if val_line_ids:
            stock_lines = (
                self.env["stock.location.quant.line"].sudo().create(val_line_ids)
            )
            line_ids = line_ids + stock_lines.ids
        return line_ids

    def add_product_into_order_line(self):
        return
        # for rec in self:
        #     product = rec.product_retail_id
        #     if rec.meters == 0:
        #         continue
        #     if not product:
        #         continue
        #     if (
        #         rec.product_retail_id.type == "consu"
        #         and rec.product_retail_id.is_storable
        #     ):
        #         list_ids = rec.add_location_line_and_check_stock_rug()
        #         if rec.location_id and list_ids:
        #             line_id = (
        #                 self.env["stock.location.quant.line"]
        #                 .sudo()
        #                 .search(
        #                     [
        #                         ("id", "in", list_ids),
        #                         ("qty_onhand_retail", ">=", rec.meters),
        #                         ("retail_location_id", "=", rec.location_id.id),
        #                     ],
        #                     limit=1,
        #                 )
        #             )
        #             have_stock_current_branch = True if line_id else False
        #             return {
        #                 "name": "Available Stock On Hand & Delivery Method",
        #                 "type": "ir.actions.act_window",
        #                 "res_model": "stock.location.quant.wizard",
        #                 "view_mode": "form",
        #                 "view_id": self.env.ref(
        #                     "modula_sale.stock_location_quant_wizard_form_view"
        #                 ).id,
        #                 "context": {
        #                     "active_ids": self.ids,
        #                     "default_quantity": rec.meters,
        #                     "default_product_retail_id": product.id,
        #                     "default_line_ids": [line_id.id]
        #                     if have_stock_current_branch
        #                     else list_ids,
        #                     "default_sale_id": rec.id,
        #                     "have_stock_current_branch": have_stock_current_branch,
        #                 },
        #                 "target": "new",
        #             }
        #         else:
        #             raise UserError(_("No stock locations were found."))
        #     else:
        #         rec.add_order_line_product()
        #         rec.product_retail_id = False
        #         rec.onchange_product_retail_id()

    def action_reject_job(self):
        self.ensure_one()
        self.state = "rejected"
        self.locked = True

    def action_refresh_default_charges(self):
        for rec in self:
            rec.order_line_charges.unlink()
            company = rec.company_id
            rec.create_charge_line_default(company)

            def create_order_line(product):
                last_sequence_line = (
                    max(self.order_line.mapped("sequence"), default=0) + 1
                )

                vals_list_line.append(
                    {
                        "name": "{}{}".format(
                            product.default_code
                            and "[%s] " % product.default_code
                            or "",
                            product.name,
                        ),
                        "product_template_id": product.id,
                        "price_unit": product.list_price,
                        "product_uom_qty": 1,
                        "order_id": rec.id,
                        "currency_id": product.currency_id.id,
                        "sequence": last_sequence_line,
                        "product_uom": product.uom_id.id,
                        "product_id": product.product_variant_id.id,
                        "is_retail": True,
                    }
                )

            for charge_line in self.env["default.charges"].search(
                [
                    ("answer_id", "!=", False),
                    ("default_charges_ids", "!=", False),
                    ("company_id", "=", rec.company_id.id),
                ]
            ):
                vals_list_line = []
                if (
                    rec.checklist_line_ids
                    and charge_line.question_id
                    and charge_line.answer_id
                ):
                    checklist_line = rec.checklist_line_ids.filtered(
                        lambda x: x.value
                        and x.question_id == charge_line.question_id
                        and charge_line.answer_id.value in x.value
                    )
                    if checklist_line:
                        for default_charges_id in charge_line.default_charges_ids:
                            create_order_line(default_charges_id)
                if len(vals_list_line) > 0:
                    self.env["sale.order.line"].sudo().create(vals_list_line)

    def _get_proforma_invoice_name(self):
        self.ensure_one()
        return f"Invoice {self.name}"

    # def _compute_invoice_status(self):
    #     res = super()._compute_invoice_status()
    #     confirmed_orders = self.filtered(
    #         lambda so: not so.state in ["draft", "sent", "cancel"]
    #     )
    #     lines_domain = [("is_downpayment", "=", False), ("display_type", "=", False)]
    #     line_invoice_status_all = [
    #         (order.id, invoice_status)
    #         for order, invoice_status in self.env["sale.order.line"]._read_group(
    #             lines_domain + [("order_id", "in", confirmed_orders.ids)],
    #             ["order_id", "invoice_status"],
    #         )
    #     ]
    #     for order in confirmed_orders:
    #         line_invoice_status = [
    #             d[1] for d in line_invoice_status_all if d[0] == order.id
    #         ]
    #         if order.state in BEFORE_INPUT_STATE:
    #             order.invoice_status = "no"
    #         elif any(
    #             invoice_status == "to invoice" for invoice_status in line_invoice_status
    #         ):
    #             if any(
    #                 invoice_status == "no" for invoice_status in line_invoice_status
    #             ):
    #                 # If only discount/delivery/promotion lines can be invoiced, the SO should not
    #                 # be invoiceable.
    #                 invoiceable_domain = lines_domain + [
    #                     ("invoice_status", "=", "to invoice")
    #                 ]
    #                 invoiceable_lines = order.order_line.filtered_domain(
    #                     invoiceable_domain
    #                 )
    #                 special_lines = invoiceable_lines.filtered(
    #                     lambda sol: not sol._can_be_invoiced_alone()
    #                 )
    #                 if invoiceable_lines == special_lines:
    #                     order.invoice_status = "no"
    #                 else:
    #                     order.invoice_status = "to invoice"
    #             else:
    #                 order.invoice_status = "to invoice"
    #         elif line_invoice_status and all(
    #             invoice_status == "invoiced" for invoice_status in line_invoice_status
    #         ):
    #             order.invoice_status = "invoiced"
    #         elif line_invoice_status and all(
    #             invoice_status in ("invoiced", "upselling")
    #             for invoice_status in line_invoice_status
    #         ):
    #             order.invoice_status = "upselling"
    #         else:
    #             order.invoice_status = "no"
    #     return res

    def action_view_purchase_orders(self):
        self.ensure_one()
        purchase_order_ids = self._get_purchase_orders()
        if not purchase_order_ids:
            purchase_order_ids = self.env["purchase.order"].search(
                [("origin", "ilike", self.name)]
            )
        else:
            additional_po = self.env["purchase.order"].search(
                [("origin", "ilike", self.name)]
            )
            purchase_order_ids |= additional_po
        purchase_order_ids = purchase_order_ids.ids
        action = {
            "res_model": "purchase.order",
            "type": "ir.actions.act_window",
        }
        if len(purchase_order_ids) == 1:
            action.update(
                {
                    "view_mode": "form",
                    "res_id": purchase_order_ids[0],
                }
            )
        else:
            action.update(
                {
                    "name": _("Purchase Order generated from %s", self.name),
                    "domain": [("id", "in", purchase_order_ids)],
                    "view_mode": "list,form",
                }
            )
        return action

    @api.depends(
        "procurement_group_id.stock_move_ids.created_purchase_line_ids.order_id",
        "procurement_group_id.stock_move_ids.move_orig_ids.purchase_line_id.order_id",
    )
    def _compute_purchase_order_count(self):
        super()._compute_purchase_order_count()
        for order in self:
            purchase_order_ids = self._get_purchase_orders()
            # if order.purchase_order_count == 0:
            #     po = self.env["purchase.order"].search(
            #         [("origin", "ilike", order.name)]
            #     )
            order.purchase_order_count = len(purchase_order_ids)

    def _get_purchase_orders(self):
        additional_po = self.env["purchase.order"].search(
            [("origin", "ilike", self.name)]
        )
        return (
            super(SaleOrder, self)._get_purchase_orders()
            | self.order_line.procurement_group_id.stock_move_ids.created_purchase_line_ids.order_id
            | self.order_line.procurement_group_id.stock_move_ids.move_orig_ids.purchase_line_id.order_id
            | additional_po
        )

    def next_page(self):
        for rec in self:
            state_tab = rec.state_tab
            if rec.state_tab == "customer":
                state_tab = "checklist"
            elif rec.state_tab == "checklist":
                state_tab = "products"
            rec._check_checklist_mandatory(state_tab)
            rec.state_tab = state_tab

    def action_view_related_orders(self):
        return {
            "name": _("Related Orders"),
            "view_mode": "list,form",
            "res_model": "sale.order",
            "type": "ir.actions.act_window",
            "domain": [("id", "in", self.related_orders_ids.ids)],
        }

    def create_company_partner(self):
        self.ensure_one()
        if self.company_partner:
            # Create parent company
            values = dict(
                name=self.company_partner, is_company=True, vat=self.partner_id.vat
            )
            values.update(
                self.partner_id._update_fields_values(self.partner_id._address_fields())
            )
            new_company = self.env["res.partner"].create(values)
            # Set new company as my parent
            self.partner_id.write(
                {
                    "parent_id": new_company.id,
                    "child_ids": [
                        Command.update(partner_id, dict(parent_id=new_company.id))
                        for partner_id in self.partner_id.child_ids.ids
                    ],
                }
            )
            self.parent_partner_id = new_company.id
        return True

    def _is_confirmation_amount_reached(self):
        self.ensure_one()
        res = super()._is_confirmation_amount_reached()
        return res

    def view_partner(self):
        self.ensure_one()
        return {
            "name": "Customer",
            "view_mode": "form",
            "res_model": "res.partner",
            "type": "ir.actions.act_window",
            "res_id": self.partner_id.id,
            "target": "current",
        }

    def update_is_retail_lines_for_imported_orders(self):
        self.ensure_one()
        self.order_line.filtered(
            lambda l: l.product_template_id.type != "service"
        ).update({"is_retail": True})

    def run_script_to_create_retail_lines_for_imported_orders(self):
        sales_imported_orders = self.search(
            [
                ("id", "in", self.ids),
                ("sale_type", "=", "retail"),
                ("is_retail_sales", "=", True),
                ("order_line", "!=", False),
            ]
        )
        for rec in sales_imported_orders:
            rec.update_is_retail_lines_for_imported_orders()

    def _has_to_be_paid(self):
        """A sale order has to be paid when:
        - its state is 'draft' or `sent`;
        - it's not expired;
        - it requires a payment;
        - the last transaction's state isn't `done`;
        - the total amount is strictly positive.

        Note: self.ensure_one()

        :return: Whether the sale order has to be paid.
        :rtype: bool
        """
        self.ensure_one()
        # transaction = self.get_portal_last_transaction()
        return (
            self.state
            in [
                "draft",
                "sent",
                "sale",
            ]
            and not self.is_expired
            and (self.require_payment or self.company_id.portal_confirmation_pay)
            # and transaction.state != 'done'
            and self.amount_total > 0
            and (self.remaining_amount != 0.0)
        )

    @api.depends("partner_id")
    def _compute_note(self):
        use_invoice_terms = (
            self.env["ir.config_parameter"]
            .sudo()
            .get_param("account.use_invoice_terms")
        )
        if not use_invoice_terms:
            return
        for order in self:
            order = order.with_company(order.company_id)
            if order.terms_type == "html" and self.env.company.invoice_terms_html:
                baseurl = html_keep_url(order._get_note_url() + "/terms")
                context = {"lang": order.partner_id.lang or self.env.user.lang}
                order.note = _(
                    """
                    <h5>Terms & Conditions</h5>
                    <p>For full details, please see our Terms & Conditions: %s</p>
                """,
                    baseurl,
                )
                del context
            elif not is_html_empty(self.env.company.invoice_terms):
                if order.partner_id.lang:
                    order = order.with_context(lang=order.partner_id.lang)
                order.note = order.env.company.invoice_terms

    def _action_cancel(self):
        res = super()._action_cancel()
        self = self.sudo()
        ops_id = (
            self.env["res.users"].search([("name", "=", "Deependra Karki")], limit=1).id
        )
        accountant_id = (
            self.env["res.users"].search([("name", "=", "Anna Li")], limit=1).id
        )
        for order in self:
            order.activity_schedule(
                "mail.mail_activity_data_warning",
                summary=_("Order %s Canceled") % (order.name),
                user_id=ops_id,
                note=_(
                    "Order %s Canceled. Please check inventory and stock, manual action might be required"
                )
                % (order.name),
            )
            order.activity_schedule(
                "mail.mail_activity_data_warning",
                summary=_("Order %s Canceled") % (order.name),
                user_id=accountant_id,
                note=_(
                    "Order %s Canceled. Please check accounting entries, manual action might be required"
                )
                % (order.name),
            )
            # reverse achieved moves
            reverse_moves = order.archieved_account_move_ids._reverse_moves()
            reverse_moves.action_post()

        # find related receipt purchase order and move valuation layer to stock
        purchase_order = self._get_purchase_orders()
        done_pickings = purchase_order.picking_ids.filtered(lambda x: x.state == "done")
        if done_pickings and done_pickings.move_ids.stock_valuation_layer_ids:
            done_pickings.move_ids.stock_valuation_layer_ids._move_valuation_layer_to_stock()
        return res

    def get_information(self):
        self.ensure_one()
        return {
            "name": "Sale Information",
            "type": "ir.actions.act_window",
            "res_model": "confirmation.dialog.wizard",
            "view_mode": "form",
            "views": [
                (
                    self.env.ref("modula_sale.sale_info_dialog_wizard_form_view").id,
                    "form",
                )
            ],
            "target": "new",
            "context": {
                "active_id": self.id,
                # 'default_retail_discount': self.detail_sale_price,
                "default_retail_margin": self.sudo().margin,
                "default_retail_margin_percent": self.sudo().margin_percent,
                "default_margin_floor": self.margin_floor,
                "default_margin_floor_percent": self.margin_floor_percent,
                "default_retail_amount_untaxed": self.amount_untaxed,
                "default_currency_id": self.currency_id.id,
                "default_minimum_deposit_total": self.minimum_deposit_total,
                "is_document": True,
                "size": "sm",
            },
        }
