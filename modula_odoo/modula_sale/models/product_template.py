# -*- coding: utf-8 -*-
import re

from odoo import api, fields, models
from odoo.osv import expression


class ProductTemplate(models.Model):
    _inherit = "product.template"
    _rec_names_search = ["name", "default_code"]

    sq_meters = fields.Float("Sq. Meters")
    floor_cost = fields.Monetary(
        "Floor Cost",
        compute="_compute_floor_cost",
        store=True,
        readonly=False,
        copy=False,
        precompute=True,
    )
    min_price = fields.Monetary("Min Price")
    display_qty_widget = fields.Boolean(compute="_compute_display_qty_widget")
    product_uom = fields.Many2one(
        comodel_name="uom.uom",
        string="Unit of Measure",
        related="product_variant_id.uom_id",
    )
    free_qty = fields.Float(
        "Free To Use Quantity ",
        digits="Product Unit of Measure",
        related="product_variant_id.free_qty",
    )
    discount_limit = fields.Float(
        string="Discount Limit", related="categ_id.discount_limit"
    )

    @api.onchange("width")
    def onchange_width(self):
        self.sq_meters = self.width / 100

    @api.depends("type")
    def _compute_display_qty_widget(self):
        """Compute the visibility of the inventory widget."""
        for line in self:
            if line.type == "consu" and line.is_storable:
                line.display_qty_widget = True
            else:
                line.display_qty_widget = False

    @api.depends("name", "default_code")
    def _compute_display_name(self):
        for template in self:
            template.display_name = "{}{}".format(
                template.default_code and "[%s] " % template.default_code or "",
                template.name,
            )

    @api.model
    def _search(self, domain, offset=0, limit=None, order=None):
        # TDE FIXME: strange
        if self._context.get("is_charge"):
            domain = expression.AND([[("is_charge", "=", True)], domain])
        return super()._search(domain, offset=offset, limit=limit, order=order)

    @api.depends("categ_id", "list_price", "taxes_id", "categ_id.floor_margin")
    def _compute_floor_cost(self):
        for product in self:
            margin = (
                product.categ_id.floor_margin
                and (product.categ_id.floor_margin / 100)
                or 0.0
            )
            # if product.taxes_id:
            #     price_included = product.taxes_id.compute_all(
            #         product.list_price,
            #         product.currency_id,
            #         1,
            #         product,
            #         self.env.user.partner_id,
            #     )["total_included"]
            # else:
            price_included = product.list_price
            product.floor_cost = price_included / (1 + margin)

    @api.model
    def _get_view(self, view_id, view_type, **options):
        arch, view = super()._get_view(view_id, view_type, **options)
        if arch.xpath("//field[@name='standard_price']"):
            arch.xpath("//field[@name='standard_price']")[0].set("groups", "account.group_account_manager")
        return arch, view


class ProductProduct(models.Model):
    _inherit = "product.product"

    floor_cost = fields.Monetary(
        "Floor Cost",
        compute="_compute_floor_cost",
        store=True,
        readonly=False,
        copy=False,
        precompute=True,
    )
    is_charge = fields.Boolean("Is Charge", related="product_tmpl_id.is_charge")

    @api.depends("categ_id", "list_price", "taxes_id", "categ_id.floor_margin")
    def _compute_floor_cost(self):
        for product in self:
            margin = (
                product.categ_id.floor_margin
                and (product.categ_id.floor_margin / 100)
                or 0.0
            )
            # if product.taxes_id:
            #     price_included = product.taxes_id.compute_all(
            #         product.list_price,
            #         product.currency_id,
            #         1,
            #         product,
            #         self.env.user.partner_id,
            #     )["total_included"]
            # else:
            price_included = product.list_price
            product.floor_cost = price_included / (1 + margin)

    @api.model
    def _search(self, domain, offset=0, limit=None, order=None):
        # TDE FIXME: strange
        if self._context.get("is_charge"):
            domain = expression.AND([[("is_charge", "=", True)], domain])
        return super()._search(domain, offset=offset, limit=limit, order=order)

    # @api.depends("name", "default_code")
    # def _compute_display_name(self):
    #     for product in self:
    #         product.display_name = product.product_tmpl_id.display_name
    @api.model
    def _get_view(self, view_id, view_type, **options):
        arch, view = super()._get_view(view_id, view_type, **options)
        if arch.xpath("//field[@name='standard_price']"):
            arch.xpath("//field[@name='standard_price']")[0].set("groups", "account.group_account_manager")
        return arch, view
