<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <t t-name="TaxTotalsField" t-inherit="account.TaxTotalsField" t-inherit-mode="extension">
        <xpath expr="//span[@name='amount_total']/parent::td" position="after">
            <t t-if="shouldShowGetInformationButton">
                <button t-on-click="(ev) => this.onGetInformationClick(ev)" t-att-name="props.method" type="button" class="btn oe_inline btn-link" t-att-title="props.title">
                    <i class="fa fa-fw o_button_icon fa-info-circle"></i>
                </button>
            </t>
        </xpath>

    </t>
</templates>