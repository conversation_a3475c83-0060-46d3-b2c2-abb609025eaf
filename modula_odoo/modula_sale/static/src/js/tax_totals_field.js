/** @odoo-module **/

import { registry } from "@web/core/registry";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";
import { useState } from "@odoo/owl";
import { TaxTotalsComponent } from "@account/components/tax_totals/tax_totals";


patch(TaxTotalsComponent.prototype, {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.notification = useService("notification");
        this.actionService = useService("action");
        
        // State to control button visibility
        this.state = useState({
            shouldShowGetInformationButton: this.env.model?.root.resModel === 'sale.order'
        });
    },

    get shouldShowGetInformationButton() {
        return this.state.shouldShowGetInformationButton;
    },

    async onGetInformationClick(ev) {
        ev.preventDefault();
        ev.stopPropagation();

        try {
            // Get the current record ID
            const recordId = this.env.model.root.resId;
            
            if (!recordId) {
                this.notification.add("Please save the record first", { type: "warning" });
                return;
            }

            // Call the backend method to get information
            const result = await this.orm.call(
                "sale.order",
                "get_information",
                [recordId]
            );

            // Display the information or perform additional actions based on the result
            if (result) {
                this.actionService.doAction(result);
            }
        } catch (error) {
            console.error("Error in Get Information button click:", error);
            this.notification.add("Error retrieving information", { type: "danger" });
        }
    }
});