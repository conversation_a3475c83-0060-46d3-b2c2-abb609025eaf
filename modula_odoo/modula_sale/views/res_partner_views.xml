<?xml version="1.0" encoding="UTF-8"?>
<odoo>

    <record model="ir.ui.view" id="partner_view_inherit">
        <field name="name">partner.view.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form" />
        <field name="arch" type="xml">
            <field name="ref" position="attributes">
                <attribute name="invisible">0</attribute>
            </field>
            <field name="category_id" position="after">
                <field name="ref" position="move"/>
                <field name="area"/>
                <field name="blue_screen_id"/>
                <field name="street_number" invisible="1" force_save="1"/>
                <field name="street_only" invisible="1" force_save="1"/>
            </field>
            <xpath expr="//field[@name='vat']" position="attributes">
                <attribute name="string">ABN</attribute>
                <attribute name="placeholder"></attribute>
            </xpath>
            <xpath expr="//div[@name='warning_tax']" position="replace">
                <div class="alert alert-warning oe_edit_only" role="alert" name="warning_tax" invisible="not same_vat_partner_id">
                    A partner with the same <span><span class="o_vat_label">ABN</span></span> already exists (<field name="same_vat_partner_id"/>), are you sure to create a new one?
                </div>
            </xpath>
        </field>
    </record>

    <record model="ir.ui.view" id="res_partner_form_view_inherit">
        <field name="name">partner.abn.view.form.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="modula_contact_extended.res_partner_form_view" />
        <field name="arch" type="xml">
            <!-- <xpath expr="//field[@name='abn']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath> -->
            <field name="carrier" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            <field name="transport_note" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="partner_tree_view_inherit">
        <field name="name">partner.view.tree.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="modula_contact_extended.view_partner_tree_inherit" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="ref"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="partner_customer_tree_view_inherit">
        <field name="name">partner.customer.view.tree.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="modula_contact_extended.view_partner_customer_tree" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="ref" optional="show"/>
                <field name="area" optional="show"/>
            </field>
            <field name="customer_type_id" position="attributes">
                <attribute name="string">Partner Type</attribute>
            </field>
            <field name="city" position="after">
                <field name="zip" optional="show" string="Post Code"/>
            </field>
        </field>
    </record>

    <record model="ir.ui.view" id="partner_filter_view_inherit">
        <field name="name">partner.view.filter.inherit</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_res_partner_filter" />
        <field name="arch" type="xml">
            <field name="email" position="after">
                <field name="ref" filter_domain="[('ref', 'ilike', self)]" string="Reference"/>
            </field>
            <filter name="partner_type_intercom" position="after">
                <filter string="Layer" name="partner_type_layer" domain="[('customer_type_id.name', '=', 'Layer')]"/>
                <filter string="Franchise" name="partner_type_franchise" domain="[('customer_type_id.name', '=', 'Franchise')]"/>
                <filter string="Sundry" name="partner_type_sundry" domain="[('customer_type_id.name', '=', 'Sundry')]"/>
            </filter>
        </field>
    </record>

    <record id="action_contacts_customer" model="ir.actions.act_window">
        <field name="name">Contacts</field>
        <field name="res_model">res.partner</field>
        <field name="view_mode">list,kanban,form,activity</field>
        <field name="search_view_id" ref="base.view_res_partner_filter"/>
        <field name="context">{'search_default_type_person': 1, 'search_default_type_customer': 1}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
            Create a Contact in your address book
          </p><p>
            Odoo helps you track all activities related to your contacts.
          </p>
        </field>
    </record>

    <record id="contacts.action_contacts" model="ir.actions.act_window">
        <field name="context">{'search_default_type_person': 1,'search_default_type_customer': 1}</field>
    </record>

</odoo>
