<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="sale_order_retail_portal_template" name="Sales Order Retails" inherit_id="portal.portal_sidebar" primary="True">
        <xpath expr="//div[hasclass('o_portal_sidebar')]" position="inside">
            <t t-set="o_portal_fullwidth_alert" groups="sales_team.group_sale_salesman">
                <!-- Uses backend_url provided in rendering values -->
                <t t-call="portal.portal_back_in_edit_mode"/>
            </t>

            <div class="row mt-3 o_portal_sale_sidebar o_portal_record_sidebar_modula">
                <!-- Sidebar -->
                <t t-call="portal.portal_record_sidebar" id="sale_order_portal_sidebar">
                    <t t-set="classes" t-value="'d-print-none col-lg-3 col-xl-4'"/>

                    <t t-set="title">
                        <h2 t-field="sale_order.amount_total" data-id="total_amount"/>
                    </t>
                    <t t-set="entries">
                        <div class="d-flex flex-column gap-4">
                            <div class="d-flex flex-column gap-2" id="sale_order_sidebar_button" style="width: 80%;">
                                <a t-if="sale_order._has_to_be_signed()" role="button" class="btn btn-primary d-block" data-bs-toggle="modal" data-bs-target="#modalaccept" href="#">
                                    <i class="fa fa-check"/><t t-if="sale_order._has_to_be_paid()"> Sign &amp; Pay</t><t t-else=""> Accept &amp; Sign</t>
                                </a>
                                <a t-elif="sale_order._has_to_be_paid()" role="button" id="o_sale_portal_paynow" data-bs-toggle="modal" data-bs-target="#modalaccept" href="#" t-att-class="'d-block %s' % ('btn btn-light' if sale_order.transaction_ids else 'btn btn-primary')">
                                    <i class="fa fa-check"/> <t t-if="not sale_order.signature">Accept &amp; Pay</t><t t-else="">Pay Now</t>
                                </a>
                                <div class="o_download_pdf d-flex gap-1 flex-lg-column flex-xl-row">
                                    <div class="flex-grow-1">
                                        <a class="btn btn-secondary d-block o_download_btn" t-att-href="sale_order.get_portal_url(suffix='/retails', report_type='pdf', download=True)" title="Download"><i class="fa fa-download"/> Download</a>
                                    </div>
                                </div>
                                <div class="o_print_pdf d-flex gap-1 flex-lg-column flex-xl-row">
                                    <div class="flex-grow-1">
                                        <a class="btn btn-secondary d-block o_print_btn o_portal_invoice_print" t-att-href="sale_order.get_portal_url(suffix='/retails', report_type='pdf')" id="print_invoice_report" title="Print" target="_blank"><i class="fa fa-print"/> Print</a>
                                    </div>
                                </div>
                            </div>

                            <div class="navspy flex-grow-1 ps-0" t-ignore="true" role="complementary">
                                <ul class="nav flex-column bs-sidenav"/>
                            </div>

                            <t t-if="not sale_order.is_expired and sale_order.state in ['draft', 'sent']">
                                <div t-if="sale_order.validity_date">
                                    <small><b>This offer expires on</b></small>
                                    <div t-field="sale_order.validity_date"/>
                                </div>
                                <div t-if="sale_order.amount_undiscounted - sale_order.amount_untaxed &gt; 0.01" class="list-group-item flex-grow-1">
                                    <small><b class="text-muted">Your advantage</b></small>
                                    <small>
                                        <b t-field="sale_order.amount_undiscounted" t-options="{&quot;widget&quot;: &quot;monetary&quot;, &quot;display_currency&quot;: sale_order.currency_id}" style="text-decoration: line-through" class="d-block mt-1" data-id="amount_undiscounted"/>
                                    </small>
                                    <t t-if="sale_order.amount_untaxed == sale_order.amount_total">
                                        <h4 t-field="sale_order.amount_total" class="text-success" data-id="total_amount"/>
                                    </t>
                                    <t t-else="">
                                        <h4 t-field="sale_order.amount_untaxed" class="text-success mb-0" data-id="total_untaxed"/>
                                        <small>(<span t-field="sale_order.amount_total" data-id="total_amount"/> Incl. tax)</small>
                                    </t>
                                </div>
                            </t>

                            <div t-if="sale_order.user_id">
                                <h6><small class="text-muted">Your contact</small></h6>
                                <div class="o_portal_contact_details d-flex flex-column gap-2">
                                    <div class="d-flex justify-content-start align-items-center gap-2">
                                        <img class="o_avatar o_portal_contact_img rounded" t-att-src="image_data_uri(sale_order.user_id.avatar_1024)" alt="Contact"/>
                                        <div>
                                            <h6 class="mb-0" t-out="sale_order.user_id.name"/>
                                            <a href="#discussion" class="d-flex align-items-center gap-2 small fw-bold">
                                            Send message
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </t>

                <!-- Page content -->
                <div id="quote_content" class="col-12 col-lg-9 col-xl-8 mt-5 mt-lg-0">

                    <!-- modal relative to the actions sign and pay -->
                    <div role="dialog" class="modal fade" id="modalaccept">
                        <div class="modal-dialog" t-if="sale_order._has_to_be_signed()">
                            <form id="accept" method="POST" t-att-data-order-id="sale_order.id" t-att-data-token="sale_order.access_token" class="js_accept_json modal-content js_website_submit_form">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <header class="modal-header">
                                    <h4 class="modal-title">Validate Order</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"/>
                                </header>
                                <main class="modal-body" id="sign-dialog">
                                    <p>
                                        <span>By signing this proposal, I agree to the following terms:</span>
                                        <ul>
                                            <li>
                                                <span>Accepted on the behalf of:</span> <b t-field="sale_order.partner_id.commercial_partner_id"/>
                                            </li>
                                            <li>
                                                <span>For an amount of:</span> <b data-id="total_amount" t-esc="sale_order.amount_total" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}"/>
                                            </li>
                                            <t t-if="sale_order.terms_conditions_ids">
                                                <li t-if="sale_order.terms_conditions_ids" id="terms">
                                                    <p>With payment terms:</p>
                                                </li>
                                            </t>
                                            <t t-else="">
                                                <li t-if="not is_html_empty(sale_order.note)" id="terms">
                                                    <span>With payment terms:</span>
                                                    <t t-if="sale_order.terms_type == 'html'">
                                                        <!-- Note is plain text. This ensures a clickable link  -->
                                                        <t t-set="tc_url" t-value="'%s/terms' % (sale_order.get_base_url())"/>
                                                        <em><a href="/terms"><t t-out="tc_url"/></a></em>
                                                    </t>
                                                    <t t-else="">
                                                        <em t-field="sale_order.note"/>
                                                    </t>
                                                </li>
                                            </t>
                                            <li t-if="sale_order.payment_term_id">
                                                <span>With payment terms:</span> <b t-field="sale_order.payment_term_id.note"/>
                                            </li>
                                        </ul>
                                        <div>
                                            <t t-set="disclaimers" t-value="sale_order.terms_conditions_ids.filtered(lambda c: 'disclaimer' in c.name.lower())"/>
                                            <t t-if="disclaimers">
                                                <div class="mb-3">
                                                    <p class="mb-0"><strong>Please Note:</strong></p>
                                                    <t t-foreach="disclaimers" t-as="disclaimer">
                                                        <div class="mb-0" t-raw="disclaimer.description"/>
                                                    </t>
                                                </div>
                                            </t>
                                            <t t-set="terms_conditions" t-value="sale_order.terms_conditions_ids.filtered(lambda c: 'disclaimer' not in c.name.lower())"/>
                                            <t t-if="terms_conditions">
                                                <t t-foreach="terms_conditions" t-as="terms">
                                                    <t t-raw="terms.description"/>
                                                </t>
                                            </t>
                                        </div>
                                    </p>
                                    <t t-call="portal.signature_form">
                                        <t t-set="call_url" t-value="sale_order.get_portal_url(suffix='/accept')"/>
                                        <t t-set="default_name" t-value="sale_order.partner_id.name"/>
                                    </t>
                                </main>
                            </form>
                        </div>

                        <div class="modal-dialog" t-if="not sale_order._has_to_be_signed() and sale_order._has_to_be_paid()">
                            <div class="modal-content">
                                <header class="modal-header">
                                    <h4 class="modal-title">Validate Order</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"/>
                                </header>
                                <main class="modal-body" id="sign-dialog">
                                    <t t-set="prepayment_amount" t-value="sale_order._get_prepayment_required_amount()"/>
                                    <t t-set="prepayment_available"
                                        t-value="sale_order and sale_order.require_payment"/>
                                    <t t-if="amount is None">
                                        <t t-set="amount" t-value="0"/>
                                    </t>
                                    <input class="prepayment-total-amount d-none" id="prepayment_total_amount" type="number" t-att-value="sale_order.amount_total"/>
                                    <input class="prepayment-remaining-amount d-none" id="prepayment_remaining_amount" type="number" t-att-value="sale_order.remaining_amount"/>
                                    <xpath expr="//div[@id='o_sale_portal_prepayment_buttons']" position="attributes">
                                        <attribute name="t-if">False</attribute>
                                    </xpath>
                                    <p>
                                        <!-- The widget associated with this modal will hide and show divs in function of the amount selected. -->
                                        <div id="o_sale_portal_use_amount_total">
                                            By paying this proposal, I agree to the following terms:
                                        </div>
                                        <div t-if="prepayment_available" id="o_sale_portal_use_amount_prepayment">
                                            <span>By paying this <u>down payment</u> of
                                            <span t-esc="prepayment_amount" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}" class="fw-bold"/>
                                            <!-- <span t-esc="prepayment_amount" class="fw-bold" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}"/> -->
                                            <!-- (<b t-esc="round(sale_order.prepayment_percent * 100, 2)"/> %) -->
                                            for this proposal, I agree to the following terms:</span>
                                        </div>
                                        <ul>
                                            <li>
                                                <span>Accepted on the behalf of:</span> <b t-field="sale_order.partner_id.commercial_partner_id"/>
                                            </li>
                                            <li>
                                                <span>For an amount of: </span><b data-id="total_amount" t-esc="sale_order.remaining_amount" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}"/>
                                            </li>
                                            <li t-if="sale_order.payment_term_id">
                                                <span>With payment terms:</span> <b t-field="sale_order.payment_term_id.note"/>
                                            </li>
                                        </ul>
                                    </p>
                                    <div t-if="company_mismatch">
                                        <t t-call="payment.company_mismatch_warning"/>
                                    </div>
                                    <div t-elif="not sale_order._has_to_be_paid()" class="alert alert-danger">
                                        The order is not in a state requiring customer payment.
                                    </div>
                                    <div t-elif="not payment_methods_sudo and not tokens_sudo" class="alert alert-warning">
                                        <strong>No suitable payment option could be found.</strong><br/>
                                        If you believe that it is an error, please contact the website administrator.
                                    </div>
                                    <div t-else="" id="payment_method" class="text-start">
                                        <h3>Pay with</h3>
                                        <t t-call="payment.form">
                                            <!-- Inject the order ID to allow Stripe to check if tokenization is required. -->
                                            <t t-set="sale_order_id" t-value="sale_order.id"/>
                                            <t t-set="mode" t-value="validation"/>
                                            <t t-set="payment_methods_sudo" t-value="payment_methods_sudo"/>
                                            <t t-set="payment_methods_finance_sudo" t-value="payment_methods_finance_sudo"/>
                                            <t t-set="payment_methods_eftpos_sudo" t-value="payment_methods_eftpos_sudo"/>
                                            <t t-set="unique_receipt_sequence" t-value="unique_receipt_sequence"/>
                                            <t t-set="sale_order" t-value="sale_order"/>
                                        </t>
                                    </div>
                                </main>
                            </div>
                        </div>
                    </div>

                    <!-- modal relative to the action reject -->
                    <div role="dialog" class="modal fade" id="modaldecline">
                        <div class="modal-dialog">
                            <form id="decline" method="POST" t-attf-action="/my/orders/#{sale_order.id}/decline?access_token=#{sale_order.access_token}" class="modal-content">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <header class="modal-header">
                                    <h4 class="modal-title">Reject This Quotation</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"/>
                                </header>
                                <main class="modal-body">
                                    <p>
                                        Tell us why you are refusing this quotation, this will help us improve our services.
                                    </p>
                                    <textarea rows="4" name="decline_message" required="" placeholder="Your feedback..." class="form-control"/>
                                </main>
                                <footer class="modal-footer">
                                    <button type="submit" t-att-id="sale_order.id" class="btn btn-danger">
                                        <i class="fa fa-times"/> Reject
                                    </button>
                                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                                        Cancel
                                    </button>
                                </footer>
                            </form>
                        </div>
                    </div>

                    <!-- status messages -->
                    <div t-if="message == 'sign_ok'" class="alert alert-success alert-dismissible d-print-none" role="status">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"/>
                        <strong>Thank You!</strong><br/>
                        <t t-if="message == 'sign_ok' and sale_order.state == 'sale'">
                            Your order has been confirmed.
                        </t>
                        <t t-elif="message == 'sign_ok' and sale_order._has_to_be_paid()">
                            Your order has been signed but still needs to be paid to be confirmed.
                        </t>
                        <t t-else="">Your order has been signed.</t>
                    </div>

                    <div t-if="message == 'cant_reject'" class="alert alert-danger alert-dismissible d-print-none" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"/>
                        Your order is not in a state to be rejected.
                    </div>

                    <div t-if="sale_order.state == 'cancel'" class="alert alert-danger alert-dismissible d-print-none" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="close"/>
                        <strong>This quotation has been canceled.</strong> <a role="button" href="#discussion"><i class="fa fa-comment"/> Contact us to get a new quotation.</a>
                    </div>

                    <div t-if="sale_order.is_expired" class="alert alert-warning alert-dismissible d-print-none" role="alert">
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="close"/>
                        <strong>This offer expired!</strong> <a role="button" href="#discussion"><i class="fa fa-comment"/> Contact us to get a new quotation.</a>
                    </div>

                    <!-- main content -->
                    <div t-attf-class="#{'pb-5' if report_type == 'html' else ''}" id="portal_sale_content">
                        <div t-call="#{sale_order._get_name_portal_content_view()}"/>
                    </div>

                    <!-- bottom actions -->

                    <!-- chatter -->
                    <hr/>
                    <div id="sale_order_communication">
                        <h3>Communication history</h3>
                        <t t-call="portal.message_thread"/>
                    </div>
                </div><!-- // #quote_content -->
            </div>
        </xpath>
    </template>

    <template id="retail_sale_signature_form" inherit_id="portal.signature_form">
        <xpath expr="//t[@t-set='signature_form_props']" position="after">
            <t t-set="signature_form_props" t-value="{
                'callUrl': call_url,
                'defaultName': default_name,
                'mode': 'draw',
                'sendLabel': send_label,
                'signatureRatio': signature_ratio,
                'signatureType': signature_type,
                'fontColor': font_color
            }"/>
        </xpath>
    </template>

    <template id="sale_order_retail_sale_order_portal_template" inherit_id="sale.sale_order_portal_template">
        <xpath expr="//t[@t-if='sale_order.get_portal_last_transaction()']" position="replace">
            <t t-set="tx" t-value="sale_order.get_portal_last_transaction()"/>
            <t t-if="tx.payment_method_code != 'wire_transfer'">
                <t t-if="sale_order.get_portal_last_transaction()">
                    <t t-call="payment.state_header">
                        <t t-set="tx" t-value="sale_order.get_portal_last_transaction()"/>
                    </t>
                </t>
            </t>
        </xpath>
        <xpath expr="//div[@id='o_sale_portal_prepayment_buttons']" position="attributes">
            <attribute name="t-if">False</attribute>
        </xpath>
        <xpath expr="//span[@t-if='prepayment_available']/parent::div" position="replace">
            <div class="mb-3">
                <!-- The widget associated with this modal will hide and show divs in function of the amount selected. -->
                <span t-if="prepayment_available">
                    <span>By paying this <u>down payment</u> of
                    <span t-esc="prepayment_amount" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}" class="fw-bold"/>
                    <!-- <span t-esc="prepayment_amount" class="fw-bold" t-options="{'widget': 'monetary', 'display_currency': sale_order.currency_id}"/> -->
                    <!-- (<b t-esc="round(sale_order.prepayment_percent * 100, 2)"/> %) -->
                    for this proposal, I agree to the following terms:</span>
                    <span id="o_sale_portal_use_amount_total">
                        By paying,
                    </span>
                </span>
                <span t-else="">
                    By paying,
                </span>
                you confirm acceptance on the behalf of <b t-field="sale_order.partner_id.commercial_partner_id"/>
                for the <b data-id="total_amount" t-field="sale_order.amount_total"/> order.
                <b t-if="sale_order.payment_term_id" t-field="sale_order.payment_term_id.note" class="o_sale_payment_terms"/>
            </div>
        </xpath>
    </template>

    <template id="sale_order_retails_portal_content" name="Sales Order Retails Portal Content">
        <!-- Intro -->
        <div id="introduction" t-attf-class="#{'border-bottom-0 pt-0 pb-3 bg-white' if report_type == 'html' else ''}">
            <div class="d-flex gap-2" id="intro_row">
                <t t-if="sale_order.state in ['sale', 'locked']">
                    <h2>
                        Tax Invoice -
                        <em t-out="sale_order.name"/>
                    </h2>
                </t>
                <t t-else="">
                    <h2>
                        <t t-out="sale_order.type_name"/> -
                        <em t-out="sale_order.name"/>
                    </h2>
                </t>
            </div>
        </div>
        <div id="content">
            <div id="informations" class="row">
                <!-- Information -->
                <div id="sale_info" class="col-12 col-lg-5 mb-4">
                    <span id="sale_info_title">
                        <h5 class="mb-1">Sale Information</h5>
                        <hr class="my-0"/>
                    </span>
                    <table class="table table-borderless table-sm">
                        <tbody style="white-space:nowrap" id="sale_info_table">
                            <tr>
                                <th t-if="sale_order.state in ['sale', 'cancel']" class="text-end pb-0">Order Date:</th>
                                <th t-else="" class="text-left pb-0">Invoice Date:</th>
                                <td class="w-100 pb-0 text-wrap"><span t-field="sale_order.date_order" t-options='{"widget": "date"}'/></td>
                            </tr>
                            <tr t-if="sale_order.validity_date">
                                <th class="text-end pb-0">Expiration Date:</th>
                                <td class="w-100 pb-0 text-wrap"><span t-field="sale_order.validity_date" t-options='{"widget": "date"}'/></td>
                            </tr>
                            <tr t-if="sale_order.client_order_ref">
                                <th class="text-end pb-0">Your Reference:</th>
                                <td class="w-100 pb-0 text-wrap"><span t-field="sale_order.client_order_ref"/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- ======  Customer Information  ====== -->
                <div id="customer_info" class="col-12 col-lg-7 mb-4">
                    <h5 class="mb-1">
                        <t t-if="sale_order.partner_shipping_id == sale_order.partner_invoice_id">
                            Invoicing and Shipping Address
                        </t>
                        <t t-else="">
                            Invoicing Address
                        </t>
                        <small t-if="sale_order.partner_id == sale_order.partner_id">
                            <a class="small" t-attf-href="/my/account?redirect={{sale_order.get_portal_url()}}">
                                <i class="fa fa-fw fa-pencil"/>
                            </a>
                        </small>
                    </h5>
                    <hr class="my-0"/>
                    <div t-field="sale_order.partner_id" t-options="{ 'widget': 'contact', 'fields': [ 'name', 'address', 'phone', 'email']}"/>
                    <span t-if="sale_order.partner_shipping_id != sale_order.partner_invoice_id"
                            id="shipping_address"
                            class="col-lg-6">
                        <br/>
                        <h5 class="mb-1">
                            Shipping Address
                        </h5>
                        <hr class="my-0"/>
                        <div t-field="sale_order.partner_shipping_id" t-options='{ "widget": "contact", "fields": [ "name", "address"]}'/>
                    </span>
                </div>
                <t t-set="invoices" t-value="sale_order.invoice_ids.filtered(lambda i: i.state not in ['draft', 'cancel']).sorted('date', reverse=True)[:3]"/>
                <div id="sale_invoices" class="row" t-if="invoices and sale_order.state in ['sale', 'cancel']">
                    <div class="col-6 col-lg-5 mb-4">
                        <h5 class="mb-1">Last Invoices</h5>
                        <hr class="mt-1 mb-2"/>
                        <t t-foreach="invoices" t-as="i">
                            <t t-set="report_url" t-value="i.get_portal_url()"/>
                            <t t-set="authorized_tx_ids" t-value="i.authorized_transaction_ids"/>
                            <div class="d-flex flex-column">
                                <div class="d-flex align-items-center justify-content-between">
                                    <a t-att-href="report_url">
                                        <span t-out="i.name"/>
                                    </a>
                                    <div t-if="i.payment_state in ('paid', 'in_payment')" class="small badge text-bg-success orders_label_text_align">
                                        <i class="fa fa-fw fa-check"/> Paid
                                    </div>
                                    <div t-elif="i.payment_state == 'reversed'" class="small badge text-bg-success orders_label_text_align">
                                        <i class="fa fa-fw fa-check"/> Reversed
                                    </div>
                                    <div t-elif="authorized_tx_ids" class="small badge text-bg-success orders_label_text_align">
                                        <i class="fa fa-fw fa-check"/> Authorized
                                    </div>
                                    <div t-else="" class="small badge text-bg-info orders_label_text_align">
                                        <i class="fa fa-fw fa-clock-o"/> Waiting Payment
                                    </div>
                                </div>
                                <div class="small d-lg-inline-block">Date: <span class="text-muted" t-field="i.invoice_date"/></div>
                            </div>
                        </t>
                    </div>
                    <t t-set="delivery_orders" t-value="sale_order.picking_ids.filtered(lambda picking: picking.picking_type_id.code == 'outgoing').sorted('date', reverse=True)[:3]"/>
                    <div t-if="delivery_orders" class="col-6 col-lg-6 mb-4">
                        <h5 class="mb-1">Last Delivery Orders</h5>
                        <hr class="mt-1 mb-2"/>
                        <div>
                            <t t-foreach="delivery_orders" t-as="picking">
                                <t t-set="delivery_report_url"
                                t-value="'/my/picking/pdf/%s?%s' % (picking.id, keep_query())"/>
                                <div name="delivery_order"
                                    class="d-flex flex-column">
                                    <div name="delivery_details" class="d-flex align-items-center justify-content-between">
                                        <a t-att-href="delivery_report_url">
                                            <span t-esc="picking.name"/>
                                        </a>
                                        <div t-if="picking.state == 'done'">
                                            <span class="small badge text-bg-success orders_label_text_align">
                                                <i class="fa fa-fw fa-truck"/> Shipped
                                            </span>
                                            <a class="badge text-bg-secondary orders_label_text_align" target="_blank"
                                                t-att-href="'/my/picking/return/pdf/%s?%s' % (picking.id, keep_query())">
                                                RETURN
                                            </a>
                                        </div>
                                        <span t-elif="picking.state == 'cancel'"
                                            class="small badge text-bg-danger orders_label_text_align">
                                            <i class="fa fa-fw fa-times"/>Cancelled
                                        </span>
                                        <span t-elif="picking.state in ['draft', 'waiting', 'confirmed', 'assigned']"
                                            class="small badge text-bg-info orders_label_text_align">
                                            <i class="fa fa-fw fa-clock-o"/>Preparation
                                        </span>
                                    </div>
                                    <div class="small d-lg-inline-block" t-if="picking.date_done or picking.scheduled_date">
                                        Date:
                                        <span class="text-muted"
                                                t-field="picking.date_done"
                                                t-options="{'date_only': True}"/>
                                        <span t-if="picking.state in ['draft', 'waiting', 'confirmed', 'assigned']"
                                                class="text-muted"
                                                t-field="picking.scheduled_date"
                                                t-options="{'date_only': True}"/>
                                    </div>
                                </div>
                            </t>
                        </div>
                    </div>
                </div>
                <t t-set="returns" t-value="sale_order.picking_ids.filtered(lambda picking: picking.picking_type_id.code == 'incoming')"/>
                <div t-if="returns" class="col-12 col-lg-6 mb-4">
                    <h4 class="mb-1">Returns</h4>
                    <hr class="mt-1 mb-2"/>
                    <t t-foreach="returns" t-as="picking">
                        <t t-set="delivery_report_url"
                            t-value="'/my/picking/pdf/%s?%s' % (picking.id, keep_query())"/>
                        <div name="return">
                            <div name="return_details" class="d-flex justify-content-between align-items-center">
                                <a t-att-href="delivery_report_url">
                                    <span t-esc="picking.name"/>
                                </a>
                                <span t-if="picking.state == 'done'"
                                    class="small badge text-bg-success orders_label_text_align">
                                    <i class="fa fa-fw fa-truck"/> Received
                                </span>
                                <span t-elif="picking.state == 'cancel'"
                                    class="small badge text-bg-danger orders_label_text_align">
                                    <i class="fa fa-fw fa-times"/> Cancelled
                                </span>
                                <span t-elif="picking.state in ['draft', 'waiting', 'confirmed', 'assigned']"
                                    class="small badge text-bg-info orders_label_text_align">
                                    <i class="fa fa-fw fa-clock-o"/> Awaiting arrival
                                </span>
                            </div>
                            <div class="small d-lg-inline-block">
                                Date:
                                <span class="text-muted"
                                    t-field="picking.date_done"
                                    t-options="{'date_only': True}"/>
                                <span t-if="picking.state in ['draft', 'waiting', 'confirmed', 'assigned']"
                                    class="text-muted"
                                    t-field="picking.scheduled_date"
                                    t-options="{'date_only': True}"/>
                            </div>
                        </div>
                    </t>
                </div>
            </div>

            <section id="details" style="page-break-inside: auto;">
                <h4 id="details_title">Details of Sale</h4>
                <t t-set="display_discount" t-value="True in [line.discount > 0 for line in sale_order.order_line]"/>
                <div class="table-responsive">
                    <table t-att-data-order-id="sale_order.id" t-att-data-token="sale_order.access_token" class="table table-sm" id="sales_order_table">
                        <thead class="bg-100">
                            <tr>
                                <th class="text-start" id="product_code_header">PRODUCT CODE</th>
                                <th class="text-start" id="product_name_header">PRODUCT DESCRIPTION</th>
                                <th class="text-end" id="product_qty_header">QTY</th>
                                <th t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                    PRICE
                                </th>
                                <th t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                    <span>DISC</span>
                                </th>
                                <th class="text-end" id="subtotal_header">
                                    <span>TOTAL</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sale_tbody">

                            <t t-set="current_subtotal" t-value="0"/>
                            <t t-set="lines_to_report" t-value="sale_order._get_order_lines_to_report()"/>

                            <t t-foreach="lines_to_report" t-as="line">

                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>

                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td id="product_code">
                                            <span t-field="line.product_template_id.default_code"/>
                                        </td>
                                        <td id="product_name">
                                            <span t-field="line.name"/>
                                        </td>
                                        <td class="text-end" id="quote_qty_td">
                                            <div id="quote_qty">
                                                <span t-field="line.product_uom_qty"/>
                                                <!-- <span t-field="line.product_uom"/> -->
                                            </div>
                                        </td>
                                        <td t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                            <div
                                                t-field="line.price_unit"
                                                t-options='{"widget": "monetary", "display_currency": sale_order.currency_id}'
                                                class="text-end"
                                            />
                                            <!-- <div t-if="line.discount">
                                                <t t-out="(1-line.discount / 100.0) * line.price_unit" t-options='{"widget": "float", "decimal_precision": "Product Price"}'/>
                                            </div> -->
                                        </td>
                                        <td t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                            <span t-if="line.discount &gt; 0">
                                                <t t-esc="(line.discount / 100) * (line.price_unit * line.product_uom_qty)" t-options='{"widget": "monetary", "display_currency": sale_order.currency_id}'/>
                                            </span>
                                        </td>
                                        <td t-if="not line.is_downpayment" class="text-end" id="subtotal">
                                        <span class="oe_order_line_price_subtotal" t-field="line.price_subtotal"/>
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td colspan="99">
                                            <span t-field="line.name"/>
                                        </td>
                                        <t t-set="current_section" t-value="line"/>
                                        <t t-set="current_subtotal" t-value="0"/>
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td colspan="99">
                                            <span t-field="line.name"/>
                                        </td>
                                    </t>
                                </tr>
                            </t>
                            <tr t-if="display_discount" class="is-subtotal text-end">
                                <td colspan="4" class="text-end">
                                    <span>TOTAL DISCOUNT</span>
                                </td>
                                <td class="text-end">
                                </td>
                                <td></td>
                            </tr>
                            <!-- <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">SURCHARGE</td>
                                <td class="text-end"><span t-field="sale_order.amount_tax"/></td>
                            </tr> -->
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">TOTAL PRICE INCL. G.S.T</td>
                                <td class="text-end"><span t-esc="sale_order.amount_untaxed + sale_order.amount_tax" t-options='{"widget": "monetary", "display_currency": sale_order.currency_id}'/></td>
                            </tr>
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">AMOUNT PAID</td>
                                <td class="text-end"><span t-esc="sale_order.amount_paid" t-options='{"widget": "monetary", "display_currency": sale_order.currency_id}'/></td>
                            </tr>
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">BALANCE OWING</td>
                                <td class="text-end"><span t-esc="sale_order.amount_untaxed + sale_order.amount_tax - sale_order.amount_paid" t-options='{"widget": "monetary", "display_currency": sale_order.currency_id}'/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
            <span id="transaction_info" class="mt-2">
                <div t-if="sale_order.get_portal_last_transaction() and sale_order.state in ('sent', 'sale')"
                    t-att-data-order-id="sale_order.id">
                    <t t-if="sale_order.transaction_ids">
                        <t t-set="tx" t-value="sale_order.get_portal_last_transaction()"/>
                        <t t-if="tx.state == 'pending'">
                            <t t-set="alert_style" t-value="'warning'"/>
                            <t t-set="status_message" t-value="tx.provider_id.sudo().pending_msg"/>
                            <div id="o_payment_status_message" class="w-100">
                                <t t-if="status_message" t-out="status_message" class="mb-0"/>
                            </div>
                        </t>
                    </t>
                </div>
            </span>

            <section t-if="not is_html_empty(sale_order.note)" id="terms" class="mt-3">
                <h3 class="">Terms &amp; Conditions</h3>
                <t t-if="sale_order.terms_type == 'html'">
                    <!-- Note is plain text. This ensures a clickable link  -->
                    <t t-set="tc_url" t-value="'%s/terms' % (sale_order.get_base_url())"/>
                    <em>For full details, please see our Terms &amp; Conditions: <a href="/terms" target="_blank"><t t-out="tc_url"/></a></em>
                </t>
                <t t-else="">
                    <em t-field="sale_order.note"/>
                </t>
            </section>

            <section t-if="sale_order.payment_term_id" class="mt-3">
                <h3 class="">Payment terms</h3>
                <hr class="mt-0 mb-1"/>
                <span t-field="sale_order.payment_term_id"/>
            </section>

            <section t-if="sale_order.signature" id="signature" name="Signature">
                <div class="row mt-4" name="signature">
                    <div t-attf-class="#{'col-3' if report_type != 'html' else 'col-sm-7 col-md-4'} ms-auto text-center">
                        <h5>Signature</h5>
                        <img t-att-src="image_data_uri(sale_order.signature)" style="max-height: 6rem; max-width: 100%;"/>
                        <p t-field="sale_order.signed_by"/>
                    </div>
                </div>
            </section>
        </div>
    </template>

    <template id="report_saleorder_document">
        <t t-call="web.external_layout">
            <t t-set="doc" t-value="doc.with_context(lang=doc.partner_id.lang)" />
            <t t-set="forced_vat" t-value="doc.fiscal_position_id.foreign_vat"/> <!-- So that it appears in the footer of the report instead of the company VAT if it's set -->
            <t t-set="address">
                <div t-field="doc.partner_id"
                    t-options='{"widget": "contact", "fields": ["address", "name"], "no_marker": True}' />
                <p t-if="doc.partner_id.vat">
                    <t t-if="doc.company_id.account_fiscal_country_id.vat_label" t-out="doc.company_id.account_fiscal_country_id.vat_label"/>
                    <t t-else="">Tax ID</t>: <span t-field="doc.partner_id.vat"/>
                </p>
            </t>
            <t t-if="doc.partner_shipping_id == doc.partner_invoice_id">
                <t t-set="information_block">
                    <strong>
                        <t t-if="doc.partner_shipping_id == doc.partner_invoice_id">
                            Invoicing and Shipping Address:
                        </t>
                        <t t-else="">
                            Invoicing Address:
                        </t>
                    </strong>
                    <div t-field="doc.partner_invoice_id"
                        t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                    <t t-if="doc.partner_shipping_id != doc.partner_invoice_id">
                        <strong>Shipping Address:</strong>
                        <div t-field="doc.partner_shipping_id"
                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                    </t>
                </t>
            </t>
            <div class="page">
                <div class="oe_structure"/>

                <h2 class="mt-4">
                    <span>Invoice # </span>
                    <span t-field="doc.name">SO0000</span>
                </h2>

                <div class="row mt-4 mb-2" id="informations">
                    <div t-if="doc.client_order_ref" class="col-auto col-3 mw-100 mb-2" name="informations_reference">
                        <strong>Your Reference:</strong><br/>
                        <span class="m-0" t-field="doc.client_order_ref">SO0000</span>
                    </div>
                    <div t-if="doc.date_order" class="col-auto col-3 mw-100 mb-2" name="informations_date">
                        <strong t-if="doc.state in ['draft', 'sent']">Quotation Date:</strong>
                        <strong t-else="">Order Date:</strong><br/>
                        <span class="m-0" t-field="doc.date_order" t-options='{"widget": "date"}'>2023-12-31</span>
                    </div>
                    <div t-if="doc.validity_date and doc.state in ['draft', 'sent']"
                        class="col-auto col-3 mw-100 mb-2"
                        name="expiration_date">
                        <strong>Expiration:</strong><br/>
                        <span class="m-0" t-field="doc.validity_date">2023-12-31</span>
                    </div>
                    <div t-if="doc.user_id.name" class="col-auto col-3 mw-100 mb-2">
                        <strong>Salesperson:</strong><br/>
                        <span class="m-0" t-field="doc.user_id">Mitchell Admin</span>
                    </div>
                </div>

                <!-- Is there a discount on at least one line? -->
                <t t-set="lines_to_report" t-value="doc._get_order_lines_to_report()"/>
                <t t-set="display_discount" t-value="any(l.discount for l in lines_to_report)"/>
                <h4 id="details_title">Details of Sale</h4>
                <div class="table-responsive">
                    <table t-att-data-order-id="doc.id" t-att-data-token="doc.access_token" class="table table-sm" id="sales_order_table">
                        <thead class="bg-100">
                            <tr>
                                <th class="text-start" id="product_code_header">PRODUCT CODE</th>
                                <th class="text-start" id="product_name_header">PRODUCT DESCRIPTION</th>
                                <th class="text-end" id="product_qty_header">QTY</th>
                                <th t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                    PRICE
                                </th>
                                <th t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                    <span>DISC</span>
                                </th>
                                <th class="text-end" id="subtotal_header">
                                    <span>TOTAL</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="sale_tbody">

                            <t t-set="current_subtotal" t-value="0"/>
                            <t t-set="lines_to_report" t-value="doc._get_order_lines_to_report()"/>

                            <t t-foreach="lines_to_report" t-as="line">

                                <t t-set="current_subtotal" t-value="current_subtotal + line.price_subtotal"/>

                                <tr t-att-class="'bg-200 fw-bold o_line_section' if line.display_type == 'line_section' else 'fst-italic o_line_note' if line.display_type == 'line_note' else ''">
                                    <t t-if="not line.display_type">
                                        <td id="product_code">
                                            <span t-field="line.product_template_id.default_code"/>
                                        </td>
                                        <td id="product_name">
                                            <span t-field="line.name"/>
                                        </td>
                                        <td class="text-end" id="quote_qty_td">
                                            <div id="quote_qty">
                                                <span t-field="line.product_uom_qty"/>
                                                <!-- <span t-field="line.product_uom"/> -->
                                            </div>
                                        </td>
                                        <td t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                            <div
                                                t-field="line.price_unit"
                                                t-options='{"widget": "monetary", "display_currency": doc.currency_id}'
                                                class="text-end"
                                            />
                                            <!-- <div t-if="line.discount">
                                                <t t-out="(1-line.discount / 100.0) * line.price_unit" t-options='{"widget": "float", "decimal_precision": "Product Price"}'/>
                                            </div> -->
                                        </td>
                                        <td t-attf-class="text-end {{ 'd-none d-sm-table-cell' if report_type == 'html' else '' }}">
                                            <span t-if="line.discount &gt; 0">
                                                <t t-esc="(line.discount / 100) * (line.price_unit * line.product_uom_qty)" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/>
                                            </span>
                                        </td>
                                        <td t-if="not line.is_downpayment" class="text-end" id="subtotal">
                                        <span class="oe_order_line_price_subtotal" t-field="line.price_subtotal"/>
                                        </td>
                                    </t>
                                    <t t-if="line.display_type == 'line_section'">
                                        <td colspan="99">
                                            <span t-field="line.name"/>
                                        </td>
                                        <t t-set="current_section" t-value="line"/>
                                        <t t-set="current_subtotal" t-value="0"/>
                                    </t>
                                    <t t-if="line.display_type == 'line_note'">
                                        <td colspan="99">
                                            <span t-field="line.name"/>
                                        </td>
                                    </t>
                                </tr>
                            </t>
                            <tr t-if="display_discount" class="is-subtotal text-end">
                                <td colspan="4" class="text-end">
                                    <span>TOTAL DISCOUNT</span>
                                </td>
                                <td class="text-end">
                                </td>
                                <td></td>
                            </tr>
                            <!-- <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">SURCHARGE</td>
                                <td class="text-end"><span t-field="doc.amount_tax"/></td>
                            </tr> -->
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">TOTAL PRICE INCL. G.S.T</td>
                                <td class="text-end"><span t-esc="doc.amount_untaxed + doc.amount_tax" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/></td>
                            </tr>
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">AMOUNT PAID</td>
                                <td class="text-end"><span t-esc="doc.amount_paid" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/></td>
                            </tr>
                            <tr class="bg-200 fw-bold o_line_section">
                                <td colspan="5" class="text-end">BALANCE OWING</td>
                                <td class="text-end">
                                <!-- <span t-esc="doc.amount_unpaid" t-options='{"widget": "monetary", "display_currency": doc.currency_id}'/> -->
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p id="transaction_info" class="mt-2 mb-2">
                    <div t-if="doc.get_portal_last_transaction() and doc.state in ('sent', 'sale')"
                        t-att-data-order-id="doc.id">
                        <t t-if="doc.transaction_ids">
                            <t t-set="tx" t-value="doc.get_portal_last_transaction()"/>
                            <t t-if="tx.state == 'pending'">
                                <t t-set="alert_style" t-value="'warning'"/>
                                <t t-set="status_message" t-value="tx.provider_id.sudo().pending_msg"/>
                                <div id="o_payment_status_message" class="w-100">
                                    <t t-if="status_message" t-out="status_message" class="mb-0"/>
                                </div>
                            </t>
                        </t>
                    </div>
                </p>
                <div class="oe_structure"></div>

                <!-- <t t-if="doc.terms_conditions_ids">
                    <section t-if="doc.terms_conditions_ids" id="terms" class="mt-3">
                        <t t-set="disclaimers" t-value="doc.terms_conditions_ids.filtered(lambda c: 'disclaimer' in c.name.lower())"/>
                        <t t-if="disclaimers">
                            <div class="mb-3">
                                <p class="mb-0"><strong>Please Note:</strong></p>
                                <t t-foreach="disclaimers" t-as="disclaimer">
                                    <div class="mb-0" t-raw="disclaimer.description"/>
                                </t>
                            </div>
                        </t>
                        <t t-set="terms_conditions" t-value="doc.terms_conditions_ids.filtered(lambda c: 'disclaimer' not in c.name.lower())"/>
                        <t t-if="terms_conditions">
                            <t t-foreach="terms_conditions" t-as="terms">
                                <t t-raw="terms.description"/>
                            </t>
                        </t>
                    </section>
                </t> -->
                <!-- <t t-else="">
                    <div>
                        <span t-field="doc.note" name="order_note"/>
                        <p t-if="not is_html_empty(doc.payment_term_id.note)">
                            <span t-field="doc.payment_term_id.note">The payment should also be transmitted with love</span>
                        </p>
                        <p t-if="doc.fiscal_position_id and not is_html_empty(doc.fiscal_position_id.sudo().note)"
                            id="fiscal_position_remark">
                            <strong>Fiscal Position Remark:</strong>
                            <span t-field="doc.fiscal_position_id.sudo().note">No further requirements for this payment</span>
                        </p>
                    </div>
                </t> -->
                <div t-if="not doc.signature" class="oe_structure"></div>
                <div t-else="" class="mt-4 ml64 mr4" name="signature">
                    <div class="offset-8">
                        <strong>Signature</strong>
                    </div>
                    <div class="offset-8">
                        <img t-att-src="image_data_uri(doc.signature)" style="max-height: 4cm; max-width: 8cm;"/>
                    </div>
                    <div class="offset-8 text-center">
                        <span t-field="doc.signed_by">Oscar Morgan</span>
                    </div>
                </div>
            </div>
            <div style="page-break-before: always;" t-if="doc.terms_conditions_ids">
                <t t-raw="doc.terms_conditions_ids[0].description"/>
            </div>
        </t>
    </template>

    <template id="portal_my_home_menu_sale_retail" inherit_id="sale.portal_my_home_menu_sale">
        <xpath expr="//li[@t-if='sale_order']/span" position="replace">
            <span t-if="sale_order.state in ['sale', 'locked']">
                <span>Tax Invoice</span>
            </span>
            <span t-else="">
                <span t-out="sale_order.type_name"/>
            </span>
        </xpath>
    </template>

    <template id="sale_order_portal_content_inherit" inherit_id="sale.sale_order_portal_content">
        <xpath expr="//t[@t-out='sale_order.type_name']" position="replace">
            <t t-if="sale_order.state in ['sale', 'locked']">
                Tax Invoice
            </t>
            <t t-else="">
                <t t-out="sale_order.type_name"/>
            </t>
        </xpath>
    </template>

    <template id="report_saleorder_invoice_retails">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="doc">
                <t t-call="modula_sale.report_saleorder_document" t-lang="doc.partner_id.lang"/>
            </t>
        </t>
    </template>

    <record id="saleorder_invoices_retails_sale" model="ir.actions.report">
        <field name="name">Invoices</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">modula_sale.report_saleorder_invoice_retails</field>
        <field name="report_file">modula_sale.report_saleorder_invoice_retails</field>
        <field name="print_report_name">('Invoice - %s' % (object.name))</field>
        <field name="attachment"/>
        <field name="binding_model_id" ref="model_sale_order"/>
        <field name="binding_type">report</field>
    </record>

</odoo>
