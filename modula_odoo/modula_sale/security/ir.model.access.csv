id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_confirmation_dialog_wizard,access_confirmation_dialog_wizard,model_confirmation_dialog_wizard,,1,1,1,1
access_checklist_checklist_manager,checklist.checklist.manager,model_checklist_checklist,base.group_system,1,1,1,1
access_checklist_checklist_user,checklist.checklist.user,model_checklist_checklist,base.group_user,1,1,1,1
access_terms_conditions_manager,terms.conditions.manager,model_terms_conditions,base.group_system,1,1,1,1
access_terms_conditions_user,terms.conditions.user,model_terms_conditions,base.group_user,1,0,0,0
access_product_product_all,product.product manager,model_product_product,,1,1,0,0
product.access_product_product_employee,product.product employee,model_product_product,base.group_user,1,1,0,0
website_sale.access_product_product_public_public,product.product.public,product.model_product_product,base.group_public,1,1,0,0
website_sale.access_product_product_public_portal,product.product.public,product.model_product_product,base.group_portal,1,1,0,0
website_sale.access_product_product_public_employee,product.product.public,product.model_product_product,base.group_user,1,1,0,0
account.access_product_product_account_user,product.product.account.user,product.model_product_product,account.group_account_readonly,1,1,0,0
purchase.access_product_product_purchase_user,product.product.purchase.user,product.model_product_product,purchase.group_purchase_user,1,1,0,0
stock.access_product_product_stock_user,product_product_stock_user,product.model_product_product,stock.group_stock_user,1,1,0,0
sale.access_product_product_sale_user,product.product sale use,product.model_product_product,sales_team.group_sale_salesman,1,1,0,0
access_default_charges_manager,default.charges.manager,model_default_charges,base.group_system,1,1,1,1
access_default_charges_user,default.charges.user,model_default_charges,base.group_user,1,0,0,0
access_survey_question_answer_survey_salesman,survey.question.answer.survey.salesman,model_survey_question_answer,sales_team.group_sale_salesman,1,1,1,1
access_survey_question_salesman,survey.question.salesman,model_survey_question,sales_team.group_sale_salesman,1,1,1,1
access_ir_sequence_all,ir_sequence all,base.model_ir_sequence,,1,1,1,0
base.access_ir_sequence_group_user,ir_sequence group_user,base.model_ir_sequence,base.group_user,1,1,1,0
