<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_inherit_sale_order" inherit_id="sale.report_saleorder_document">
        <xpath expr="//table//th[@name='th_description']" position="before">
            <th class="text-start">Product</th>
        </xpath>
        <xpath expr="//table//td[@name='td_name']" position="before">
            <td name="td_product"><span t-field="line.product_id.name"></span></td>
        </xpath>
        <!-- <xpath expr="//t/div[1]/div[8]" position="replace">
            <span t-field="doc.note" t-if="not doc.terms_conditions_ids" t-attf-style="#{'text-align:justify;text-justify:inter-word;' if doc.company_id.terms_type != 'html' else ''}" name="order_note"/>
            <p t-if="not is_html_empty(doc.payment_term_id.note) or not doc.terms_conditions_ids">
                <span t-field="doc.payment_term_id.note">The payment should also be transmitted with love</span>
            </p>
            <div class="oe_structure"/>
            <p t-if="doc.fiscal_position_id and not is_html_empty(doc.fiscal_position_id.sudo().note)" id="fiscal_position_remark">
                <strong>Fiscal Position Remark:</strong>
                <span t-field="doc.fiscal_position_id.sudo().note">No further requirements for this payment</span>
            </p>
        </xpath> -->
        <xpath expr="//div[hasclass('page')]" position="after">
            <div style="page-break-before: always;" t-if="doc.terms_conditions_ids">
                <t t-raw="doc.terms_conditions_ids[0].description"/>
            </div>
        </xpath>
    </template>
</odoo>
