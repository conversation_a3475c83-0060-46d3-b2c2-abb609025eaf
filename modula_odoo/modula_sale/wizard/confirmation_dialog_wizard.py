from odoo import api, fields, models


class ConfirmationDialogWizard(models.TransientModel):
    _name = "confirmation.dialog.wizard"
    _description = "Confirmation Dialog Wizard"

    def _get_description(self):
        sale_order = (
            self.env["sale.order"].sudo().browse(self.env.context.get("active_id"))
        )
        description = ""
        # if sale_order:
        #     underlay = sale_order.product_retail_id.default_underlay_code_id
        #     code = underlay and f"({underlay.default_code})" or ""
        #     description = (
        #         "Do you wish to add the default underlay " + code + " for this product?"
        #     )
        return description

    description = fields.Text("Description", default=_get_description)
    attachment_id = fields.Many2one("ir.attachment", "Attachment")
    retail_discount = fields.Float("Discount")
    retail_margin = fields.Monetary("Margin", groups="account.group_account_manager")
    retail_amount_untaxed = fields.Monetary(string="Untaxed Amount")
    retail_margin_percent = fields.Float(
        "Margin (%)", groups="account.group_account_manager"
    )
    currency_id = fields.Many2one(comodel_name="res.currency")
    margin_floor = fields.Monetary("Floor Margin")
    margin_floor_percent = fields.Float("Floor Margin (%)")
    minimum_deposit_total = fields.Monetary()


    def action_confirmation_dialog(self):
        self.attachment_id.unlink()

    def action_add_default_underlay(self):
        # Add default underlay to the current sale order base on product retail
        if self.env.context.get("active_id"):
            sale_order = (
                self.env["sale.order"].sudo().browse(self.env.context.get("active_id"))
            )
            sale_order.add_order_line_product()
            sale_order.add_order_line_product(underlay=True)
            sale_order.product_retail_id = False
            sale_order.onchange_product_retail_id()

    def action_without_default_underlay(self):
        if self.env.context.get("active_id"):
            sale_order = (
                self.env["sale.order"].sudo().browse(self.env.context.get("active_id"))
            )
            sale_order.add_order_line_product()
            sale_order.product_retail_id = False
            sale_order.onchange_product_retail_id()
