# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, api, fields, models
from odoo.exceptions import UserError


class SaleOrderCancel(models.TransientModel):
    _inherit = "sale.order.cancel"

    amount_paid = fields.Monetary(compute="_compute_amount_paid", string="Amount Paid")
    currency_id = fields.Many2one(related="order_id.currency_id")

    refund_type = fields.Selection(
        selection=[
            ("no", "No Refund"),
            ("partial", "Partial Refund"),
            ("full", "Full Refund"),
        ],
        default="no",
        string="Refund Type",
        required=True,
    )
    refund_amount = fields.Float(string="Refund Amount")

    @api.depends("order_id")
    def _compute_amount_paid(self):
        for rec in self:
            rec.amount_paid = sum(
                rec.order_id.downpayment_ids.filtered(
                    lambda x: x.state in ["paid", "in_process"]
                ).mapped("amount_signed")
            )

    @api.onchange("refund_type")
    def _onchange_refund_type(self):
        if self.refund_type == "full":
            self.refund_amount = self.order_id.amount_paid

    def action_cancel(self):
        res = super(SaleOrderCancel, self).action_cancel()

        # auto wip journal entry
        debit_account_id = (
            self.env["account.account"]
            .search([("code", "=", "M-000-8701-0000-980")], limit=1)
            .id
        )
        credit_account_id = (
            self.env["account.account"]
            .search([("code", "=", "M-003-2204-0000-930")], limit=1)
            .id
        )
        wip_journal = (
            self.env["account.journal"].search([("code", "=", "WIP")], limit=1).id
        )
        for order in self.order_id:
            amount = (
                self.refund_amount
                if self.refund_type == "partial"
                else self.amount_paid
            )
            wip_move = self.env["account.move"].create(
                {
                    # 'name': f'{order.name} - Cancellation',
                    "journal_id": wip_journal,
                    "date": fields.Date.context_today(self),
                    "ref": order.name,
                    "line_ids": [
                        (
                            0,
                            0,
                            {
                                "account_id": debit_account_id,
                                "name": f"{order.name} - Cancellation",
                                "debit": amount,
                            },
                        ),
                        (
                            0,
                            0,
                            {
                                "account_id": credit_account_id,
                                "name": f"{order.name} - Cancellation",
                                "credit": amount,
                            },
                        ),
                    ],
                }
            )
            wip_move.action_post()
        return res
