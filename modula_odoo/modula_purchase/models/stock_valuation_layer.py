from odoo import api, fields, models, tools


class StockValuationLayer(models.Model):
    _inherit = "stock.valuation.layer"

    def _move_valuation_layer_to_stock(self):
        for layer in self:
            layer.duplicate_with_account_move()
        return True

    def duplicate_with_account_move(self):
        """
        Duplicate the current valuation layer and create a related account.move (journal entry),
        linking them together. The move will use the same value and reference as the original layer.
        """
        self.ensure_one()

        # Find the Stock Valuation Journal (code 'STJ')
        journal = self.env["account.journal"].search(
            [("code", "=", "STJ"), ("company_id", "=", self.company_id.id)], limit=1
        )
        if not journal:
            raise ValueError(
                "Stock Valuation Journal (STJ) not found for this company."
            )

        # Determine accounts (example: use product category accounts)
        product = self.product_id
        container_id = self.stock_move_id.purchase_line_id.order_id.container_id
        if not container_id or not container_id.fiscal_position_id:
            return False
        mto_account_id = container_id.fiscal_position_id.account_ids.filtered(
            lambda x: x.is_mto
        ).account_dest_id
        stock_account_id = container_id.fiscal_position_id.account_ids.filtered(
            lambda x: not x.is_mto
        ).account_dest_id
        # stock_account = product.categ_id.property_stock_valuation_account_i
        # suspense_account = product.categ_id.property_stock_account_input_categ_id
        # if not stock_account or not suspense_account:
        #     raise ValueError("Stock or suspense account not set on product category.")
        if not mto_account_id or not stock_account_id:
            return False
        # Create the account.move (journal entry)
        move_vals = {
            "journal_id": journal.id,
            "ref": f"Duplicate of {self.reference or self.id}",
            "move_type": "entry",
            "line_ids": [
                (
                    0,
                    0,
                    {
                        "name": f"Stock Valuation Layer Debit",
                        "account_id": mto_account_id.id,
                        "balance": -self.value,
                    },
                ),
                (
                    0,
                    0,
                    {
                        "name": f"Stock Valuation Layer Credit",
                        "account_id": stock_account_id.id,
                        "balance": self.value,
                    },
                ),
            ],
            "date": fields.Date.today(),
        }
        # account_move = self.env["account.move"].create(move_vals)

        # Optionally, link the move to the new layer (if you have a field for this)
        # new_layer.account_move_id = account_move.id

        # Duplicate the valuation layer
        account_move = self.env["account.move"].create(move_vals)
        new_layer = self.copy(
            {"value": 0, "remaining_value": 0, "account_move_id": account_move.id}
        )
        account_move.action_post()
        return True
