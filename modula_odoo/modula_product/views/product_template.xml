<?xml version="1.0"?>
<odoo>

    <!-- <record id="product_template_form_view_inherit" model="ir.ui.view">
        <field name="name">product.template.form.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='alternative_product_ids']" position="after">
                <field name="skus_product_ids" widget="many2many_tags" domain="[('is_sku', '=', True)]" invisible="not is_range"/>
                <field name="is_sku"/>
                <field name="is_range"/>
            </xpath>
        </field>
    </record> -->

    <record id="product_template_form_view_branch" model="ir.ui.view">
        <field name="name">product.extended.product.form.branch</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_form_view" />
        <field name="arch" type="xml">
            <group name="group_general" position="inside">
                <!-- <field name="x_description_2"/>
                <field name="x_design"/> -->
                <!-- <field name="x_colour_name"/> -->
                <!-- <field name="x_room_size" string="Size"/> -->
                <field name="charge_type" invisible="not is_charge"/>
                <!-- <field name="cobol_charge_type"/> -->
                <field name="is_charge" invisible="1"/>
                <!-- <field name="product_master_id"/>
                <field name="public_type_id"/> -->
            </group>
            <xpath expr="//label[@for='name']" position="before">
                <label for="default_code" string="Internal Reference"/>
                <h1>
                    <div class="d-flex">
                        <field class="text-break" name="default_code"/>
                    </div>
                </h1>
            </xpath>
            <group name="group_general" position="inside">
                <field name="width"/>
                <!-- <field name="length"/> -->
                <field name="depth"/>
                <field name="height"/>
                <field name="seat_height"/>
                <field name="is_charge" invisible="1"/>
                <field name="is_sku"/>
                <field name="is_range"/>
                <field name="skus_product_ids" widget="many2many_tags" domain="[('is_sku', '=', True)]" invisible="not is_range" required="is_range"/>
                <!-- <field name="product_source"/>
                <field name="default_underlay_code" invisible="1"/>
                <field name="default_underlay_code_id"/>
                <label string="Discontinue Flag" for="discontinue_flag" invisible="not discontinue_flag" class="change_color_red"/>
                <label string="Discontinue Flag" for="discontinue_flag" invisible="discontinue_flag"/>
                <field name="discontinue_flag" nolabel="1"/> -->
            </group>
            <group name="group_standard_price" position="inside">
                <!-- <field name="franchise_name"/>
                <field name="franchise_colour"/>
                <field name="runner_flag"/> -->
                <!-- <field name="sqm_pack"/> -->
                <field name="rug_design"/>
                <!-- <field name="size_code"/> -->
            </group>
            <div name="standard_price_uom" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </div>
            <label for="standard_price" position="attributes">
                <attribute name="groups">account.group_account_manager</attribute>
            </label>
        </field>
    </record>

    <record id="stock.product_template_action_product" model="ir.actions.act_window">
        <field name="view_mode">list,kanban,form</field>
    </record>

    <record id="product_template_treeview_edit" model="ir.ui.view">
        <field name="name">product.template.product.tree</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_tree_view"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='is_favorite']" position="after">
                <xpath expr="//field[@name='default_code']" position="move"/>
            </xpath>
            <xpath expr="//field[@name='uom_id']" position="after">
                <xpath expr="//field[@name='list_price']" position="move"/>
                <xpath expr="//field[@name='standard_price']" position="move"/>
                <xpath expr="//field[@name='categ_id']" position="move"/>
                <xpath expr="//field[@name='company_id']" position="move"/>
                <xpath expr="//field[@name='product_tag_ids']" position="move"/>
                <xpath expr="//field[@name='barcode']" position="move"/>
                <xpath expr="//field[@name='type']" position="move"/>
                <xpath expr="//list[1]/field[@name='type']" position="move"/>
            </xpath>
            <xpath expr="//field[@name='product_tag_ids']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="attributes">
                <attribute name="optional">show</attribute>
            </xpath>
            <xpath expr="//field[@name='categ_id']" position="attributes">
                <attribute name="optional">show</attribute>
            </xpath>

        </field>
    </record>

    <!-- <record id="product_template_kanban_view_inherit" model="ir.ui.view">
        <field name="name">product.template.product.kanban.inherit</field>
        <field name="model">product.template</field>
        <field name="inherit_id" ref="product.product_template_kanban_view"/>
        <field name="arch" type="xml">
            <xpath expr="//main/div[1]" position="after">
                <div>
                    <t t-if="record.colour_name.value"><field name="colour_name"/></t>
                </div>
            </xpath>
        </field>
    </record> -->


    <record id="action_clean_variant_attribute" model="ir.actions.server">
        <field name="name">Clean Variant Attributes</field>
        <field name="model_id" ref="product.model_product_template"/>
        <field name="binding_model_id" ref="product.model_product_template"/>
        <field name="state">code</field>
        <field name="code">
            action = records.clean_variant_attribute()
        </field>
        <field name="binding_type">action</field>
        <field name="usage">ir_actions_server</field>
    </record>

    <record id="product_template_search_view_inherit" model="ir.ui.view">
		<field name="name">product.template.search.view.inherit</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="product.product_template_search_view"/>
		<field name="arch" type="xml">
            <filter name="services" position="before">
                <filter string="Range" name="range" domain="[('is_range','=',True)]"/>
                <filter string="Sku's" name="skus" domain="[('is_sku','=',True)]"/>
                <separator/>
            </filter>
 		</field>
	</record>
    <record id="product_template_form_view" model="ir.ui.view">
		<field name="name">product.template.form.inherit</field>
		<field name="model">product.template</field>
		<field name="inherit_id" ref="account.product_template_form_view"/>
		<field name="arch" type="xml">
            <div name="list_price_uom" position="inside">
                <field name="tax_string" position="move"/>
            </div>
 		</field>
	</record>

</odoo>
