# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import _, api, Command, fields, models
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_round, float_is_zero



class ReturnPicking(models.TransientModel):
    _inherit= 'stock.return.picking'

    def _create_return(self):
        new_picking = super(ReturnPicking, self)._create_return()
        new_picking.employee_id = False
        return new_picking