# -*- coding: utf-8 -*-

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class StockPicking(models.Model):
    _inherit = "stock.picking"

    required_payment_before_delivery = fields.<PERSON><PERSON>an(
        string="Required Payment Before Delivery",
        compute="_compute_required_payment_before_delivery",
    )
    has_been_paid_before_delivery = fields.<PERSON><PERSON>an(
        string="Has Been Paid Before Delivery",
        compute="_compute_has_been_paid_before_delivery",
    )
    checklist_input_html = fields.Html(related="sale_id.checklist_input_html")
    is_checklist_mandatory = fields.Bo<PERSON>an(
        string="Is Checklist Mandatory",
        related="sale_id.is_checklist_mandatory",
    )
    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
        tracking=True,
    )
    # delivery_address = fields.Char(
    #     string="Delivery Address", compute="_compute_delivery_address", store=True
    # )

    # @api.depends("partner_id")
    # def _compute_delivery_address(self):
    #     for picking in self:
    #         if not picking.partner_id:
    #             picking.delivery_address = False
    #         else:
    #             picking.delivery_address = picking.partner_id._display_address()

    def _compute_required_payment_before_delivery(self):
        for picking in self:
            if picking.sale_id and picking.picking_type_code == "outgoing":
                payment_term = picking.sale_id.payment_term_id
                if payment_term.payment_required_before_delivery:
                    picking.required_payment_before_delivery = True
                else:
                    picking.required_payment_before_delivery = False
            else:
                picking.required_payment_before_delivery = False

    def _compute_has_been_paid_before_delivery(self):
        for picking in self:
            if picking.required_payment_before_delivery:
                if picking.sale_id and picking._validate_payment_before_delivery():
                    picking.has_been_paid_before_delivery = True
                else:
                    picking.has_been_paid_before_delivery = False
            else:
                picking.has_been_paid_before_delivery = False

    def _validate_payment_before_delivery(self):
        if not self.sale_id:
            return False

        mto_route = self.env.ref("stock.route_warehouse0_mto")

        for sale_orderline in self.move_ids.sale_line_id:
            if not sale_orderline.fully_paid_received:
                return False
        return True

    def button_validate(self):
        if (
            self.required_payment_before_delivery
            and not self.has_been_paid_before_delivery
        ):
            raise UserError(
                _(
                    "Payment requirement is not met. Delivery Order cannot be validated until fully paid"
                )
            )
        return super(StockPicking, self).button_validate()