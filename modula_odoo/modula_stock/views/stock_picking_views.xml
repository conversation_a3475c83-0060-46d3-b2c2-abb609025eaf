<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_picking_form_inherit_add_payment_details" model="ir.ui.view">
        <field name="name">view.picking.form.inherit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <field name="backorder_id" position="after">
                <field name="required_payment_before_delivery" invisible="1"/>
                <field name="has_been_paid_before_delivery" invisible="1"/>
            </field>
            <xpath expr="//field[@name='origin']" position="after">
                <field name="employee_id" readonly="1"/>
            </xpath>
            <!-- <xpath expr="//field[@name='partner_id']" position="after">
                <field name="delivery_address" invisible="picking_type_code != 'outgoing'"/>
            </xpath> -->
            <xpath expr="//field[@name='partner_id']" position="attributes">
                <attribute name="widget">res_partner_many2one</attribute>
                <attribute name="context">{'res_partner_search_mode': 'customer', 'show_address': 1, 'show_vat': True}</attribute>
            </xpath>
            <page name='note' position="after">
                <page string="Delivery Checklist" name="checklist" invisible="not is_checklist_mandatory">
                    <div class="o_field_custom checklist_form_custom" id="checklist_form">
                        <field name="checklist_input_html"/>
                    </div>
                </page>
			</page>
        </field>
    </record>
</odoo>
