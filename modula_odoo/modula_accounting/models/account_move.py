# -*- coding: utf-8 -*-
from audioop import rms
from datetime import date, datetime, timedelta
from math import floor

from odoo import _, api, fields, models
from odoo.exceptions import UserError
from odoo.tools import float_compare
from pytz import UTC, timezone


class AccountMove(models.Model):
    _inherit = "account.move"

    inverse_rate = fields.Float(
        string="Convert Rate",
        compute="_compute_inverse_rate",
        readonly=False,
        store=True,
    )
    show_update_rate_btn = fields.Boolean(
        string="Show Update Rate Button", compute="_compute_show_update_rate_btn"
    )
    # container_ids = fields.Many2many(
    #     "container.container",
    #     "container_account_move_rel",
    #     "container_id",
    #     "move_id",
    #     string="Containers",
    # )
    # container_count = fields.Integer(
    #     string="Container Count", compute="_compute_container_count"
    # )
    # container_line_ids = fields.One2many(
    #     "container.line", "order_no", string="Container Lines"
    # )
    bill_asset_ids = fields.Many2many(
        "account.asset",
        "account_asset_bill_rel",
        "move_id",
        "asset_id",
        string="Assets",
    )
    bill_asset_count = fields.Integer(
        string="Asset Count", compute="_compute_bill_asset_count"
    )

    collection_code = fields.Selection(
        selection=[
            ("statement_issued", "Statement Issued"),
            ("overdue_notice", "Overdue Notice"),
            ("retention", "Retention"),
            ("service_complaints", "Service Complaints"),
            ("manufacturing_complaints", "Manufacturing Complaints"),
            ("legal_final_notice_issued", "Legal - Final Notice Issued"),
            ("legal_summons_served", "Legal - Summons Served"),
            ("legal_s364_notice_served", "Legal - s364 Notice Served"),
            ("legal_trial_date_set", "Legal - Trial Date Set"),
            ("legal_judgment_obtained", "Legal - Judgment Obtained"),
            ("legal_warrant_issued", "Legal - Warrant Issued"),
            ("legal_bankruptcy_notice_issued", "Legal - Bankruptcy Notice Issued"),
            ("legal_oral_exam_performed", "Legal - Oral Exam Performed"),
        ],
        string="Collection Code",
    )
    is_account_manager = fields.Boolean(
        string="Is Account Manager", compute="_compute_is_account_manager"
    )
    # extra_outstanding_credits_debits_widget = fields.Binary(
    #     string="Extra Outstanding Credits Debits Widget",
    #     compute="_compute_extra_outstanding_credits_debits_widget",
    # )
    subledger_required_fields = fields.Char(
        string="Subledger Required Fields",
        compute="_compute_subledger_required_fields",
        compute_sudo=True,
    )
    subledger_sale_id = fields.Many2one(
        "sale.order",
        string="Subledger Sale",
    )
    subledger_payment_id = fields.Many2one(
        "account.payment",
        string="Subledger Payment",
        domain="[('sale_order_id', '=', subledger_sale_id)]",
    )
    matched = fields.Boolean(
        string="Matched", help="If the bill is matched with a picking"
    )
    approved_for_payment = fields.Boolean(
        string="Approved for Payment", help="If the bill is approved for payment"
    )
    depn_rate = fields.Float(
        related="asset_id.depn_rate", store=True, group_operator="avg"
    )
    asset_model_id = fields.Many2one(related="asset_id.model_id", store=True)
    reverse_date = fields.Date(
        string="Reverse Date",
        help="Date when the move is reversed",
        copy=False,
    )
    asset_tax_id = fields.Many2one(
        "account.asset",
        string="Asset Tax",
    )
    asset_description = fields.Text(
        string="Asset Description",
        related="asset_id.description",
    )
    currency_converted = fields.Boolean(
        string="Currency Converted",
        default=False,
    )

    @api.depends("line_ids.balance")
    def _compute_depreciation_value(self):
        res = super(AccountMove, self)._compute_depreciation_value()
        for move in self.filtered(
            lambda m: not m.asset_id
            and not m.reversed_entry_id.asset_id
            and m.asset_tax_id
        ):
            asset = move.asset_tax_id
            if asset:
                account_internal_group = "expense"
                asset_depreciation = sum(
                    move.line_ids.filtered(
                        lambda l: l.account_id.internal_group == account_internal_group
                        or l.account_id == asset.account_depreciation_expense_id
                    ).mapped("balance")
                )
                # Special case of closing entry - only disposed assets of type 'purchase' should match this condition
                # The condition on len(move.line_ids) is to avoid the case where there is only one depreciation move, and it is not a disposal move
                # The condition will be matched because a disposal move from a disposal move will always have more than 2 lines, unlike a normal depreciation move
                if (
                    any(
                        line.account_id == asset.account_asset_id
                        and float_compare(
                            -line.balance,
                            asset.original_value,
                            precision_rounding=asset.currency_id.rounding,
                        )
                        == 0
                        for line in move.line_ids
                    )
                    and len(move.line_ids) > 2
                ):
                    asset_depreciation = (
                        asset.original_value
                        - asset.salvage_value
                        - (
                            move.line_ids[1].debit
                            if asset.original_value > 0
                            else move.line_ids[1].credit
                        )
                        * (-1 if asset.original_value < 0 else 1)
                    )
            else:
                asset_depreciation = 0
            move.depreciation_value = asset_depreciation
        return res

    @api.depends(
        "asset_id",
        "depreciation_value",
        "asset_id.total_depreciable_value",
        "asset_id.already_depreciated_amount_import",
        "state",
        "asset_tax_id",
        "asset_tax_id.total_depreciable_value",
        "asset_tax_id.already_depreciated_amount_import",
    )
    def _compute_depreciation_cumulative_value(self):
        res = super(AccountMove, self)._compute_depreciation_cumulative_value()
        self.filtered(lambda m: m.asset_tax_id).asset_depreciated_value = 0
        self.filtered(lambda m: m.asset_tax_id).asset_remaining_value = 0

        # make sure to protect all the records being assigned, because the
        # assignments invoke method write() on non-protected records, which may
        # cause an infinite recursion in case method write() needs to read one
        # of these fields (like in case of a base automation)
        fields = [
            self._fields["asset_remaining_value"],
            self._fields["asset_depreciated_value"],
        ]
        with self.env.protecting(fields, self.asset_tax_id.asset_tax_line_ids):
            for asset in self.asset_tax_id:
                depreciated = 0
                remaining = (
                    asset.total_depreciable_value
                    - asset.already_depreciated_amount_import
                )
                for move in asset.asset_tax_line_ids.sorted(
                    lambda mv: (mv.date, mv._origin.id)
                ):
                    if move.state != "cancel":
                        remaining -= move.depreciation_value
                        depreciated += move.depreciation_value
                    move.asset_remaining_value = remaining
                    move.asset_depreciated_value = depreciated
        return res

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if not vals.get("name", False) and vals.get("origin_payment_id", False):
                payment = self.env["account.payment"].browse(vals["origin_payment_id"])
                vals["name"] = payment.name
        return super(AccountMove, self).create(vals_list)

    @api.constrains("state", "asset_id")
    def _constrains_check_asset_state(self):
        return

    def _search_default_journal(self):
        journal = super(AccountMove, self)._search_default_journal()
        if self.env.context.get("journal_code"):
            journal = self.env["account.journal"].search(
                [
                    ("code", "=", self.env.context.get("journal_code")),
                    ("company_id", "=", self.company_id.id),
                ],
                limit=1,
            )
        return journal

    @api.depends("line_ids.account_id", "journal_id.required_field_ids")
    def _compute_subledger_required_fields(self):
        subledger_journal = self.env["account.journal"].search(
            [("code", "=", "SUB"), ("company_id", "=", self.company_id.id)], limit=1
        )
        for rec in self:
            rec.subledger_required_fields = ""
            if rec.journal_id and rec.journal_id == subledger_journal:
                if not rec.journal_id.required_field_ids:
                    continue
                account_ids = rec.line_ids and rec.line_ids.account_id
                if account_ids:
                    required_field_lines = rec.journal_id.required_field_ids.filtered(
                        lambda x: x.account_id in account_ids
                    )
                    rec.subledger_required_fields = (
                        required_field_lines
                        and required_field_lines.field_ids.mapped("name")
                    )

    def action_ledger_view_source_sale_orders(self):
        source_orders = self.subledger_sale_id
        result = self.env["ir.actions.act_window"]._for_xml_id("sale.action_orders")
        if len(source_orders) > 1:
            result["domain"] = [("id", "in", source_orders.ids)]
        elif len(source_orders) == 1:
            result["views"] = [(self.env.ref("sale.view_order_form", False).id, "form")]
            result["res_id"] = source_orders.id
        else:
            result = {"type": "ir.actions.act_window_close"}
        return result

    @api.depends("line_ids.sale_line_ids", "subledger_sale_id")
    def _compute_origin_so_count(self):
        res = super()._compute_origin_so_count()
        for move in self:
            move.sale_order_count += len(move.subledger_sale_id)
        return res

    def _compute_is_account_manager(self):
        for rec in self:
            rec.is_account_manager = self.env.user.has_group(
                "account.group_account_manager"
            )

    @api.onchange("partner_id")
    def onchange_partner_id(self):
        if self.partner_id:
            self.is_account_manager = self.env.user.has_group(
                "account.group_account_manager"
            )
        else:
            self.is_account_manager = False

    @api.depends("bill_asset_ids")
    def _compute_bill_asset_count(self):
        for rec in self:
            rec.bill_asset_count = len(rec.bill_asset_ids)

    def open_assets(self):
        self.ensure_one()
        action = self.env["ir.actions.actions"]._for_xml_id(
            "account_asset.action_account_asset_form"
        )
        action["domain"] = [("id", "in", self.bill_asset_ids.ids)]
        return action

    # @api.depends("container_line_ids.container_id")
    # def _compute_container_ids(self):
    #     for rec in self:
    #         rec.container_ids = rec.container_line_ids.mapped("container_id")

    # def _compute_container_count(self):
    #     for rec in self:
    #         rec.container_count = len(rec.container_ids)

    # def action_open_container(self):
    #     self.ensure_one()
    #     # add default move_ids to the context
    #     context = dict(self._context)
    #     context.update({"default_move_ids": [(6, 0, [self.id])]})
    #     # need to hide the moves if the user is not from HOLDING
    #     if 1 not in self.env.companies.ids:
    #         context.update({"hide_move": True})
    #     if len(self.container_ids) == 1:
    #         return {
    #             "name": "Container",
    #             "type": "ir.actions.act_window",
    #             "view_mode": "form",
    #             "res_model": "container.container",
    #             "res_id": self.container_ids.id,
    #             "context": context,
    #         }
    #     return {
    #         "name": "Containers",
    #         "type": "ir.actions.act_window",
    #         "view_mode": "list,form",
    #         "res_model": "container.container",
    #         "domain": [("id", "in", self.container_ids.ids)],
    #         "context": context,
    #     }

    @api.depends("currency_id", "date")
    def _compute_inverse_rate(self):
        for rec in self:
            date = rec.date or fields.Date.today()
            rec.inverse_rate = rec.currency_id._get_conversion_rate(
                from_currency=rec.company_currency_id,
                to_currency=rec.currency_id,
                date=date,
            )

    @api.depends("company_currency_id", "currency_id", "currency_converted")
    def _compute_show_update_rate_btn(self):
        for rec in self:
            if (
                rec.company_currency_id != rec.currency_id
                and not rec.currency_converted
            ):
                rec.show_update_rate_btn = True
            else:
                rec.show_update_rate_btn = False

    def recompute_currency_rate(self):
        self.ensure_one()
        # self.company_id.update_currency_rates()
        # self._compute_inverse_rate()
        self.convert_to_currency()

    def convert_to_currency(self):
        self.ensure_one()
        inverse_rate = self.inverse_rate
        # log convert note
        self.message_post(
            body="Converted from %s to %s with rate %s"
            % (self.company_currency_id.name, self.currency_id.name, inverse_rate)
        )
        # self.currency_id = self.company_currency_id
        for line in self.invoice_line_ids:
            line.write({"price_unit": line.price_unit * inverse_rate}),
        self.currency_converted = True
        return

    def action_confirm_asset_modify(self):
        self.ensure_one()
        context = dict(self._context)
        if "create_from_asset_modify" in context and "active_asset" in context:
            asset = self.env["account.asset"].browse(self._context["active_asset"])
            context.pop("create_from_asset_modify")
            context.pop("active_asset")
        if not asset:
            raise UserError("No asset found!")
        if self.state != "posted":
            self.action_post()
        context.update(
            {
                "default_invoice_ids": [(6, 0, [self.id])],
                "continue_selling_asset": True,
            }
        )

        return asset.with_context(context).action_asset_modify()

    def action_post(self):
        if self.env.context.get("check_allow_post") and self.move_type == "entry":
            if any(
                not line.account_id.allow_direct_posting
                for line in self.line_ids
                if line.account_id
            ):
                # get the account that does not allow direct posting
                account_code = self.line_ids.filtered(
                    lambda x: not x.account_id.allow_direct_posting
                ).account_id.code
                raise UserError(
                    "This Account is a Control Account and does not allow Direct Posting. Account: %s"
                    % account_code
                )
        # check if there is any asset line
        asset_lines = self.invoice_line_ids.filtered(lambda x: x.default_asset_id)
        if asset_lines:
            total_rounding = 0
            for line in asset_lines:
                total_rounding += line.price_subtotal - floor(line.price_subtotal)
            rounding_account_id = self.company_id.account_asset_rounding_id
            if total_rounding:
                vals = {
                    "name": "Rounding",
                    "price_unit": total_rounding,
                    "quantity": 1,
                    "account_id": rounding_account_id,
                    "move_id": self.id,
                }
                self.invoice_line_ids += self.invoice_line_ids.new(vals)
                for line in asset_lines:
                    line.price_unit = floor(line.price_subtotal)
        return super().action_post()

    def _post(self, soft=True):
        # OVERRIDE
        # Auto-reconcile the invoice with payments coming from transactions.
        # It's useful when you have a "paid" sale order (using a payment transaction) and you invoice it later.

        posted = super()._post(soft)
        for invoice in posted.filtered(lambda move: move.is_invoice()):
            # domain = [
            #     (
            #         "account_id",
            #         "in",
            #         invoice.commercial_partner_id.payment_account_id.ids,
            #     ),
            #     ("parent_state", "=", "posted"),
            #     ("partner_id", "=", invoice.commercial_partner_id.id),
            #     ("reconciled", "=", False),
            #     ("move_type", "=", "entry"),
            #     ("balance", "<", 0.0),
            #     "|",
            #     ("amount_residual", "!=", 0.0),
            #     ("amount_residual_currency", "!=", 0.0),
            # ]
            line_to_reconciles = []
            related_payment_lines = invoice.transaction_ids.payment_id.move_id.line_ids
            if related_payment_lines:
                line_to_reconciles = related_payment_lines.filtered(
                    lambda x: x.account_id
                    == invoice.commercial_partner_id.payment_account_id
                    if invoice.commercial_partner_id.payment_account_id
                    else invoice.company_id.payment_account_id
                    and not x.reconciled
                    and x.move_id.id != invoice.id
                    and x.parent_state == "posted"
                    and x.move_type == "entry"
                    and x.balance < 0
                    and x.amount_residual != 0
                )
            if line_to_reconciles:
                for line in line_to_reconciles:
                    invoice.js_assign_outstanding_line(line.id)

        # reverse the move if reverse date is set
        default_values_list = []
        move_to_reverse = posted.filtered(
            lambda x: x.reverse_date and not x.reversal_move_ids
        )
        for rec in move_to_reverse:
            default_values_list.append(
                {
                    "ref": _("Reversal of: %s", rec.name),
                    "date": rec.reverse_date,
                    "invoice_date_due": rec.reverse_date,
                    "invoice_date": rec.is_invoice(include_receipts=True)
                    and (rec.reverse_date or rec.reverse_date)
                    or False,
                }
            )
        if move_to_reverse:
            new_moves = move_to_reverse._reverse_moves(default_values_list, cancel=True)
            move_to_reverse._message_log_batch(
                bodies={
                    move.id: move.env._(
                        "This entry has been %s",
                        reverse._get_html_link(title=move.env._("reversed")),
                    )
                    for move, reverse in zip(move_to_reverse, new_moves)
                }
            )
        return posted

    @api.model
    def _cleanup_write_orm_values(self, record, vals):
        if vals.get("partner_type"):
            del vals["partner_type"]
        return super(AccountMove, self)._cleanup_write_orm_values(record, vals)

    # def _compute_extra_outstanding_credits_debits_widget(self):
    #     # res = super(AccountMove, self)._compute_payments_widget_to_reconcile_info()
    #     for move in self:
    #         move.extra_outstanding_credits_debits_widget = False
    #         move.invoice_has_outstanding = move.invoice_has_outstanding
    #         if move.is_inbound():
    #             payments_widget_vals = {
    #                 "outstanding": True,
    #                 "content": [],
    #                 "move_id": move.id,
    #                 "title": _("Outstanding credits"),
    #             }
    #             domain = [
    #                 (
    #                     "account_id",
    #                     "=",
    #                     move.commercial_partner_id.payment_account_id.id
    #                     if move.commercial_partner_id.payment_account_id
    #                     else move.company_id.payment_account_id.id,
    #                 ),
    #                 ("parent_state", "=", "posted"),
    #                 ("partner_id", "=", move.commercial_partner_id.id),
    #                 ("reconciled", "=", False),
    #                 "|",
    #                 ("amount_residual", "!=", 0.0),
    #                 ("amount_residual_currency", "!=", 0.0),
    #                 ("balance", "<", 0.0),
    #             ]
    #             for line in self.env["account.move.line"].search(domain):
    #                 if line.currency_id == move.currency_id:
    #                     # Same foreign currency.
    #                     amount = abs(line.amount_residual_currency)
    #                 else:
    #                     # Different foreign currencies.
    #                     amount = line.company_currency_id._convert(
    #                         abs(line.amount_residual),
    #                         move.currency_id,
    #                         move.company_id,
    #                         line.date,
    #                     )
    #                 if move.currency_id.is_zero(amount):
    #                     continue
    #                 payments_widget_vals["content"].append(
    #                     {
    #                         "journal_name": line.ref or line.move_id.name,
    #                         "amount": amount,
    #                         "currency_id": move.currency_id.id,
    #                         "id": line.id,
    #                         "move_id": line.move_id.id,
    #                         "date": fields.Date.to_string(line.date),
    #                         "account_payment_id": line.payment_id.id,
    #                     }
    #                 )
    #             if not payments_widget_vals["content"]:
    #                 continue
    #             # if move.invoice_outstanding_credits_debits_widget['content']:
    #             move.extra_outstanding_credits_debits_widget = payments_widget_vals
    #             move.invoice_has_outstanding = True
    #     # return res

    # @api.depends("move_type", "line_ids.amount_residual")
    # def _compute_payments_widget_reconciled_info(self):
    #     res = super(AccountMove, self)._compute_payments_widget_reconciled_info()
    #     for move in self:
    #         if not move.invoice_payments_widget:
    #             move.invoice_payments_widget = {
    #                 "title": _("Less Payment"),
    #                 "outstanding": False,
    #                 "content": [],
    #             }
    #     return res

    def js_assign_outstanding_line(self, line_id):
        res = super(AccountMove, self).js_assign_outstanding_line(line_id)
        lines = self.env["account.move.line"].browse(line_id)
        lines += self.line_ids.filtered(
            lambda line: line.account_id
            == self.partner_id.with_company(
                self.company_id
            ).property_account_receivable_id
            and not line.reconciled
            and "Job Completed" not in line.name
        )
        context = dict(self.env.context)
        context.update({"skip_check_account_id": True})
        if lines.payment_id:
            self.matched_payment_ids += lines.payment_id
        return lines.with_context(context).reconcile()

    @api.depends("invoice_date", "company_id", "move_type")
    def _compute_date(self):
        vendor_bill_ids = self.filtered(lambda x: x.move_type == "in_invoice")
        for move in vendor_bill_ids:
            move.date = fields.Date.context_today(self)
        return super(AccountMove, self - vendor_bill_ids)._compute_date()


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    is_subcontract_service = fields.Boolean(
        string="Is Subcontract Service",
        compute="_compute_is_subcontract_service",
        store=True,
    )
    name = fields.Char(inverse="_inverse_name")
    gst_amount = fields.Monetary(
        string="GST Amount",
        compute="_compute_gst_amount",
        groups="account.group_account_manager",
    )

    asset_id = fields.Many2one(
        "account.asset", string="Asset", related="move_id.asset_id", store=True
    )

    def _compute_gst_amount(self):
        for line in self:
            line.gst_amount = line.price_total - line.price_subtotal

    # def _get_reconciliation_aml_field_value(self, field, shadowed_aml_values):
    #     if field == 'account_id' and self._context.get('skip_check_account_id'):
    #         return False
    #     return super(AccountMoveLine, self)._get_reconciliation_aml_field_value(field, shadowed_aml_values)

    def _check_amls_exigibility_for_reconciliation(self, shadowed_aml_values=None):
        if not self:
            return
        if not self._context.get("skip_check_account_id"):
            return super(
                AccountMoveLine, self
            )._check_amls_exigibility_for_reconciliation(shadowed_aml_values)
        return

    def _inverse_name(self):
        for line in self:
            if line.is_subcontract_service:
                line.sale_line_ids.name = line.name

    @api.depends("sale_line_ids.is_subcontract_service")
    def _compute_is_subcontract_service(self):
        for line in self:
            if line.sale_line_ids:
                line.is_subcontract_service = True
                for sale_line in line.sale_line_ids:
                    if not sale_line.is_subcontract_service:
                        line.is_subcontract_service = False
                        break

    def edit_service_note(self):
        # self.ensure_one()
        return {
            "name": "Edit Service Note",
            "type": "ir.actions.act_window",
            "res_model": "account.move.line",
            "view_mode": "form",
            "res_id": self.id,
            "view_id": self.env.ref(
                "modula_accounting.account_move_line_service_note_form"
            ).id,
            "target": "new",
        }

    def _compute_account_id(self):
        res = super(AccountMoveLine, self)._compute_account_id()
        context = dict(self.env.context)
        if not context.get("create_from_asset_modify"):
            return res
        if context.get("asset_default_account_id"):
            for line in self:
                if not line.display_type in ("product", "line_section", "line_note"):
                    continue
                line.account_id = context.get("asset_default_account_id")
        return res

    def _compute_name(self):
        res = super(AccountMoveLine, self)._compute_name()
        context = dict(self.env.context)
        if not context.get("create_from_asset_modify"):
            return res
        if context.get("asset_default_name"):
            for line in self:
                if not line.display_type in ("product", "line_section", "line_note"):
                    continue
                line.name = context.get("asset_default_name")
        return res


##TODO: odoo 18 seem to handle this by default? need validate and remove later
# from odoo.addons.account.models.account_move_line import AccountMoveLine as AML
# from odoo.tools import frozendict


# @api.depends(
#     "tax_ids",
#     "currency_id",
#     "partner_id",
#     "analytic_distribution",
#     "balance",
#     "partner_id",
#     "move_id.partner_id",
#     "price_unit",
#     "quantity",
# )
# def _compute_all_tax(self):
#     for line in self:
#         sign = line.move_id.direction_sign
#         if line.display_type == "tax":
#             line.compute_all_tax = {}
#             line.compute_all_tax_dirty = False
#             continue
#         if line.display_type == "product" and line.move_id.is_invoice(True):
#             amount_currency = sign * line.price_unit * (1 - line.discount / 100)
#             handle_price_include = True
#             quantity = line.quantity
#         else:
#             amount_currency = line.amount_currency
#             handle_price_include = False
#             quantity = 1
#         compute_all_currency = line.tax_ids.compute_all(
#             amount_currency,
#             currency=line.currency_id,
#             quantity=quantity,
#             product=line.product_id,
#             partner=line.move_id.partner_id or line.partner_id,
#             is_refund=line.is_refund,
#             handle_price_include=handle_price_include,
#             include_caba_tags=line.move_id.always_tax_exigible,
#             fixed_multiplicator=sign,
#         )
#         rate = line.amount_currency / line.balance if line.balance else 1
#         line.compute_all_tax_dirty = True
#         line.compute_all_tax = {
#             frozendict(
#                 {
#                     "tax_repartition_line_id": tax["tax_repartition_line_id"],
#                     "group_tax_id": tax["group"] and tax["group"].id or False,
#                     "account_id": tax["account_id"] or line.account_id.id,
#                     "currency_id": line.currency_id.id,
#                     "analytic_distribution": (
#                         tax["analytic"] or not tax["use_in_tax_closing"]
#                     )
#                     and line.analytic_distribution,
#                     "tax_ids": [(6, 0, tax["tax_ids"])],
#                     "tax_tag_ids": [(6, 0, tax["tag_ids"])],
#                     "partner_id": line.move_id.partner_id.id or line.partner_id.id,
#                     "move_id": line.move_id.id,
#                     "display_type": line.display_type,
#                 }
#             ): {
#                 "name": tax["name"]
#                 + (" " + _("(Discount)") if line.display_type == "epd" else ""),
#                 "balance": tax["amount"] / rate,
#                 "amount_currency": tax["amount"],
#                 "tax_base_amount": tax["base"]
#                 / rate
#                 * (-1 if line.tax_tag_invert else 1),
#             }
#             for tax in compute_all_currency["taxes"]
#             # if tax['amount'] Monkeypatch here, create tax line even if amount is 0
#         }
#         if not line.tax_repartition_line_id:
#             line.compute_all_tax[frozendict({"id": line.id})] = {
#                 "tax_tag_ids": [(6, 0, compute_all_currency["base_tags"])],
#             }


# AML._compute_all_tax = _compute_all_tax
