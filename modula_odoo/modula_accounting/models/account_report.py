# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from collections import defaultdict
from datetime import datetime

from odoo import _, api, fields, models


class AccountReportLine(models.Model):
    _inherit = "account.report.line"

    def _expand_groupby(
        self,
        line_dict_id,
        groupby,
        options,
        offset=0,
        limit=None,
        load_one_more=False,
        unfold_all_batch_data=None,
    ):
        group_lines = super()._expand_groupby(
            line_dict_id,
            groupby,
            options,
            offset,
            limit,
            load_one_more,
            unfold_all_batch_data,
        )
        if self.display_name not in [
            "Aged Receivable",
            "Aged Payable",
        ] or self._context.get("show_unknow", False):
            return group_lines
        # hide Unknown from Aged Receivables Report
        for group_line in group_lines:
            if group_line.get("name") == "Unknown":
                group_lines.remove(group_line)
        return group_lines


class AccountReport(models.Model):
    _inherit = "account.report"

    filter_account_segment = fields.Boolean(
        string="Account Segment",
        compute=lambda x: x._compute_report_option_filter("filter_account_segment"),
        readonly=False,
        store=True,
        default=True,
        depends=["root_report_id", "section_main_report_ids"],
    )
    filter_account_header = fields.Boolean(
        string="Account Header",
        compute=lambda x: x._compute_report_option_filter("filter_account_header"),
        readonly=False,
        store=True,
        default=True,
        depends=["root_report_id", "section_main_report_ids"],
    )

    def _compute_report_option_filter(self, field_name, default_value=False):
        res = super()._compute_report_option_filter(field_name, default_value)
        if field_name == "filter_account_segment":
            res = True
        elif field_name == "filter_account_header":
            res = True
        return res

    def _init_options_segment(self, options, previous_options):
        if not self.filter_account_segment:
            return

        segment_lists = self.env["account.segment"].search([])
        options["segment"] = [
            {"id": segment.id, "name": segment.name} for segment in segment_lists
        ]
        # previous_segment_ids = previous_options.get("segment_ids") or []
        previous_segment_ids = []
        for segment in segment_lists:
            previous_segment_ids += (
                previous_options.get(str(segment.id) + "_segment_ids") or []
            )

        # previous_segment_ids = [int(partner) for partner in previous_segment_ids]
        # search instead of browse so that record rules apply and filter out the ones the user does not have access to
        selected_segments = (
            previous_segment_ids
            and self.env["account.segment.line"]
            .with_context(active_test=False)
            .search([("id", "in", previous_segment_ids)])
            or self.env["account.segment.line"]
        )
        # edit selected_segments to be a list of dicts with segment_id and segment_lines
        dict_segments = {segment.id: [] for segment in segment_lists}
        for segment in selected_segments:
            dict_segments[segment.segment_id.id].append(segment.id)
        options["selected_segment_ids"] = list(dict_segments.values())
        for segment, segment_lines in dict_segments.items():
            options[str(segment) + "_segment_ids"] = segment_lines

    def _init_options_account_header(self, options, previous_options):
        if not self.filter_account_header:
            return

        options["account_header"] = True
        previous_account_header_ids = previous_options.get("account_header_ids") or []

        selected_account_header_ids = [
            int(account_header) for account_header in previous_account_header_ids
        ]
        # search instead of browse so that record rules apply and filter out the ones the user does not have access to
        selected_account_headers = (
            selected_account_header_ids
            and self.env["account.header"]
            .with_context(active_test=False)
            .search([("id", "in", selected_account_header_ids)])
            or self.env["account.header"]
        )
        options["selected_account_header_ids"] = selected_account_headers.mapped("name")
        options["account_header_ids"] = selected_account_headers.ids

    def _init_options_fiscal_year(self, options, previous_options):
        # if not self.filter_fiscal_year:
        #     return
        options["fiscal_year"] = True
        options["fiscal_period"] = True
        previous_fiscal_year_id = previous_options.get("fiscal_year_id") or []
        previous_fiscal_period_id = previous_options.get("fiscal_period_id") or []
        # search instead of browse so that record rules apply and filter out the ones the user does not have access to
        selected_fiscal_year = (
            previous_fiscal_year_id
            and self.env["modula.fiscal.year"]
            .with_context(active_test=False)
            .search([("id", "=", previous_fiscal_year_id)])
            or self.env["modula.fiscal.year"]
        )
        selected_fiscal_period = (
            previous_fiscal_period_id
            and self.env["modula.account.period"]
            .with_context(active_test=False)
            .search([("id", "=", previous_fiscal_period_id)])
            or self.env["modula.account.period"]
        )
        options["fiscal_year_id"] = selected_fiscal_year.id
        options["fiscal_period_id"] = selected_fiscal_period.id

    @api.model
    def _get_options_segment_domain(self, options):
        domain = []
        if options.get("selected_segment_ids"):
            for segment in options["selected_segment_ids"]:
                if segment:
                    domain.append(("account_id.segment_ids", "in", segment))
        return domain

    @api.model
    def _get_options_account_header_domain(self, options):
        domain = []
        if options.get("account_header_ids"):
            domain += [
                "|",
                ("account_id.header_account_id", "in", options["account_header_ids"]),
                (
                    "account_id.parent_header_account_id",
                    "in",
                    options["account_header_ids"],
                ),
            ]
        return domain

    def _get_options_domain(self, options, date_scope):
        domain = super()._get_options_domain(options, date_scope)
        domain += self._get_options_segment_domain(options)
        domain += self._get_options_account_header_domain(options)
        return domain

    def get_options(self, previous_options):
        options = super().get_options(previous_options)
        options["print_format"] = previous_options.get("print_format")
        return options

    def _init_options_unfolded(self, options, previous_options):
        res = super()._init_options_unfolded(options, previous_options)
        options["unfold_all"] = self.filter_unfold_all and previous_options.get(
            "unfold_all", False
        )

        previous_section_source_id = previous_options.get("sections_source_id")
        if (
            not previous_section_source_id
            or previous_section_source_id == options["sections_source_id"]
        ):
            # Only keep the unfolded lines if they belong to the same report or a section of the same report
            options["folded_lines"] = previous_options.get("folded_lines", [])
        else:
            options["folded_lines"] = []
        return res

    # aged payable and receivable, partner ledger date domain change
    def _get_options_date_domain(self, options, date_scope):
        scope_domain = super()._get_options_date_domain(options, date_scope)
        if self.name not in (
            "Aged Receivable",
            "Aged Payable",
            "Partner Ledger",
        ) or not options.get("aging_based_on"):
            return scope_domain
        new_domain = []
        aging_date_field = (
            "invoice_date"
            if options["aging_based_on"] == "base_on_invoice_date"
            else "date_maturity"
        )

        for domain in scope_domain:
            if domain[0] == "date":
                new_domain.append((aging_date_field, domain[1], domain[2]))
            else:
                new_domain.append(domain)
        return new_domain

    ###TODO: what does this do? review this, why overwrite the whole function?
    # # Override _expand_groupby need to hide Unknown from Aged Receivables Report
    # def _expand_groupby(
    #     self,
    #     line_dict_id,
    #     groupby,
    #     options,
    #     offset=0,
    #     limit=None,
    #     load_one_more=False,
    #     unfold_all_batch_data=None,
    # ):
    #     """Expand function used to get the sublines of a groupby.
    #     groupby param is a string consisting of one or more coma-separated field names. Only the first one
    #     will be used for the expansion; if there are subsequent ones, the generated lines will themselves used them as
    #     their groupby value, and point to this expand_function, hence generating a hierarchy of groupby).
    #     """
    #     self.ensure_one()

    #     group_indent = 0
    #     line_id_list = self.report_id._parse_line_id(line_dict_id)

    #     # If this line is a sub-groupby of groupby line (for example, when grouping by partner, id; the id line is a subgroup of partner),
    #     # we need to add the domain of the parent groupby criteria to the options
    #     prefix_groups_count = 0
    #     sub_groupby_domain = []
    #     full_sub_groupby_key_elements = []
    #     for markup, model, value in line_id_list:
    #         if markup.startswith("groupby:"):
    #             field_name = markup.split(":")[1]
    #             sub_groupby_domain.append((field_name, "=", value))
    #             full_sub_groupby_key_elements.append(f"{field_name}:{value}")
    #         elif markup.startswith("groupby_prefix_group:"):
    #             prefix_groups_count += 1

    #         if model == "account.group":
    #             group_indent += 1

    #     if sub_groupby_domain:
    #         forced_domain = options.get("forced_domain", []) + sub_groupby_domain
    #         options = {**options, "forced_domain": forced_domain}

    #     # Parse groupby
    #     groupby_data = self._parse_groupby(options, groupby_to_expand=groupby)
    #     groupby_model = groupby_data["current_groupby_model"]
    #     next_groupby = groupby_data["next_groupby"]
    #     current_groupby = groupby_data["current_groupby"]

    #     # If the report transmitted custom_unfold_all_batch_data dictionary, use it
    #     full_sub_groupby_key = (
    #         f"[{self.id}]{','.join(full_sub_groupby_key_elements)}=>{current_groupby}"
    #     )

    #     cached_result = (unfold_all_batch_data or {}).get(full_sub_groupby_key)

    #     if cached_result is not None:
    #         all_column_groups_expression_totals = cached_result
    #     else:
    #         all_column_groups_expression_totals = (
    #             self.report_id._compute_expression_totals_for_each_column_group(
    #                 self.expression_ids,
    #                 options,
    #                 groupby_to_expand=groupby,
    #                 offset=offset,
    #                 limit=limit + 1 if limit and load_one_more else limit,
    #             )
    #         )

    #     # Put similar grouping keys from different totals/periods together, so that we don't display multiple
    #     # lines for the same grouping key

    #     figure_types_defaulting_to_0 = {"monetary", "percentage", "integer", "float"}

    #     default_value_per_expr_label = {
    #         col_opt["expression_label"]: 0
    #         if col_opt["figure_type"] in figure_types_defaulting_to_0
    #         else None
    #         for col_opt in options["columns"]
    #     }

    #     # Gather default value for each expression, in case it has no value for a given grouping key
    #     default_value_per_expression = {}
    #     for expression in self.expression_ids:
    #         if expression.figure_type:
    #             default_value = (
    #                 0
    #                 if expression.figure_type in figure_types_defaulting_to_0
    #                 else None
    #             )
    #         else:
    #             default_value = default_value_per_expr_label.get(expression.label)

    #         default_value_per_expression[expression] = {"value": default_value}

    #     # Build each group's result
    #     aggregated_group_totals = defaultdict(
    #         lambda: defaultdict(default_value_per_expression.copy)
    #     )
    #     for (
    #         column_group_key,
    #         expression_totals,
    #     ) in all_column_groups_expression_totals.items():
    #         for expression in self.expression_ids:
    #             for grouping_key, result in expression_totals[expression]["value"]:
    #                 aggregated_group_totals[grouping_key][column_group_key][
    #                     expression
    #                 ] = {"value": result}

    #     # Generate groupby lines
    #     group_lines_by_keys = {}
    #     for grouping_key, group_totals in aggregated_group_totals.items():
    #         # For this, we emulate a dict formatted like the result of _compute_expression_totals_for_each_column_group, so that we can call
    #         # _build_static_line_columns like on non-grouped lines
    #         line_id = self.report_id._get_generic_line_id(
    #             groupby_model,
    #             grouping_key,
    #             parent_line_id=line_dict_id,
    #             markup=f"groupby:{current_groupby}",
    #         )
    #         group_line_dict = {
    #             # 'name' key will be set later, so that we can browse all the records of this expansion at once (in case we're dealing with records)
    #             "id": line_id,
    #             "unfoldable": bool(next_groupby),
    #             "unfolded": (next_groupby and options["unfold_all"])
    #             or line_id in options["unfolded_lines"],
    #             "groupby": next_groupby,
    #             "columns": self.report_id._build_static_line_columns(
    #                 self, options, group_totals
    #             ),
    #             "level": self.hierarchy_level
    #             + 2 * (prefix_groups_count + len(sub_groupby_domain) + 1)
    #             + (group_indent - 1),
    #             "parent_id": line_dict_id,
    #             "expand_function": "_report_expand_unfoldable_line_with_groupby"
    #             if next_groupby
    #             else None,
    #             "caret_options": groupby_model if not next_groupby else None,
    #         }

    #         if self.report_id.custom_handler_model_id:
    #             self.env[
    #                 self.report_id.custom_handler_model_name
    #             ]._custom_groupby_line_completer(
    #                 self.report_id, options, group_line_dict
    #             )

    #         # Growth comparison column.
    #         if self.report_id._display_growth_comparison(options):
    #             compared_expression = self.expression_ids.filtered(
    #                 lambda expr: expr.label
    #                 == group_line_dict["columns"][0]["expression_label"]
    #             )
    #             group_line_dict[
    #                 "growth_comparison_data"
    #             ] = self.report_id._compute_growth_comparison_column(
    #                 options,
    #                 group_line_dict["columns"][0]["no_format"],
    #                 group_line_dict["columns"][1]["no_format"],
    #                 green_on_positive=compared_expression.green_on_positive,
    #             )

    #         group_lines_by_keys[grouping_key] = group_line_dict

    #     # Sort grouping keys in the right order and generate line names
    #     keys_and_names_in_sequence = {}  # Order of this dict will matter

    #     if groupby_model:
    #         browsed_groupby_keys = self.env[groupby_model].browse(
    #             list(key for key in group_lines_by_keys if key is not None)
    #         )

    #         out_of_sorting_record = None
    #         records_to_sort = browsed_groupby_keys
    #         if (
    #             browsed_groupby_keys
    #             and load_one_more
    #             and len(browsed_groupby_keys) >= limit
    #         ):
    #             out_of_sorting_record = browsed_groupby_keys[-1]
    #             records_to_sort = records_to_sort[:-1]

    #         for record in records_to_sort.with_context(active_test=False).sorted():
    #             keys_and_names_in_sequence[record.id] = record.display_name

    #         if None in group_lines_by_keys:
    #             # hide Unknown from Aged Receivables Report
    #             if self.display_name == "Aged Receivable":
    #                 if "show_unknow" in self._context and self._context["show_unknow"]:
    #                     keys_and_names_in_sequence[None] = _("Unknown")
    #                 else:
    #                     del group_lines_by_keys[None]
    #             else:
    #                 keys_and_names_in_sequence[None] = _("Unknown")

    #         if out_of_sorting_record:
    #             keys_and_names_in_sequence[
    #                 out_of_sorting_record.id
    #             ] = out_of_sorting_record.display_name

    #     else:
    #         for non_relational_key in sorted(group_lines_by_keys.keys()):
    #             keys_and_names_in_sequence[non_relational_key] = (
    #                 str(non_relational_key)
    #                 if non_relational_key is not None
    #                 else _("Unknown")
    #             )

    #     # Build result: add a name to the groupby lines and handle totals below section for multi-level groupby
    #     group_lines = []
    #     for grouping_key, line_name in keys_and_names_in_sequence.items():
    #         group_line_dict = group_lines_by_keys[grouping_key]
    #         group_line_dict["name"] = line_name
    #         group_lines.append(group_line_dict)

    #     if options.get("hierarchy"):
    #         group_lines = self.report_id._create_hierarchy(group_lines, options)

    #     return group_lines

    def _inject_report_into_xlsx_sheet(self, options, workbook, sheet):
        if self.name in ("DM202", "DM211", "DM212"):  # Store the original method
            # make a dict that map the header name to the header + date_from + date_to
            header_date_dict = {}
            for header in options["column_headers"][0]:
                header_date_dict[header["name"]] = [
                    header["name"],
                    datetime.strptime(
                        header["forced_options"]["date"]["date_from"], "%Y-%m-%d"
                    ).strftime("%d/%m/%Y"),
                    datetime.strptime(
                        header["forced_options"]["date"]["date_to"], "%Y-%m-%d"
                    ).strftime("%d/%m/%Y"),
                ]
            original_write_cell = sheet.write
            original_merge_range = sheet.merge_range

            def custom_merge_range(x, y, colspan, rowspan, value, style):
                if value in header_date_dict:
                    new_value = header_date_dict[value]
                    sheet.write(x, y, new_value[0], style)
                    sheet.write(x, y + 1, "From:", style)
                    sheet.write(x, y + 2, new_value[1], style)
                    sheet.write(x, y + 3, "To:", style)
                    sheet.write(x, y + 4, new_value[2], style)
                    return
                return original_merge_range(x, y, colspan, rowspan, value, style)

            # Override write_cell to skip writing Code and Account Name headers
            def custom_write_cell(x, y, value, style, colspan=1, datetime=False):
                if value == "Code":
                    return
                if value == "Account Name":
                    value = "Asset Number"
                return original_write_cell(x, y, value, style)

            # Replace the write method temporarily
            sheet.write = custom_write_cell
            sheet.merge_range = custom_merge_range
        # Call the original method
        res = super()._inject_report_into_xlsx_sheet(options, workbook, sheet)

        # Restore the original write method
        if self.name in ("DM202", "DM211", "DM212"):
            sheet.write = original_write_cell
            sheet.merge_range = original_merge_range
        return res
