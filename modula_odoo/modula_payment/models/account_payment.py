from datetime import datetime

from odoo import _, api, fields, models


class AccountPayment(models.Model):
    _inherit = "account.payment"

    ref = fields.Char(string="Reference", related="move_id.ref")

    @api.model
    def default_get(self, fields):
        rec = super(AccountPayment, self).default_get(fields)

        if not rec.get("branch_id", False) and rec.get("sale_order_id", False):
            sale_order_id = self.env["sale.order"].browse(rec["sale_order_id"])
            rec["branch_id"] = sale_order_id.branch_id.id
        return rec

    @api.depends(
        "name",
        "journal_id",
        "partner_id",
        "partner_type",
        "partner_id.payment_account_id",
        "company_id.payment_account_id",
    )
    def _compute_destination_account_id(self):
        res = super(AccountPayment, self)._compute_destination_account_id()
        for pay in self:
            if pay.partner_type == "customer":
                pay.destination_account_id = (
                    pay.partner_id
                    and pay.partner_id.with_company(pay.company_id).payment_account_id
                    or pay.company_id.payment_account_id
                    or pay.destination_account_id
                )
        return res

    @api.depends(
        "payment_method_line_id",
        "partner_id.payment_outstanding_account_id",
        "payment_type",
    )
    def _compute_outstanding_account_id(self):
        pay_to_update = self.filtered(
            lambda p: p.payment_type == "outbound"
            and p.partner_id
            and p.partner_id.payment_outstanding_account_id
        )
        for pay in pay_to_update:
            pay.outstanding_account_id = pay.partner_id.payment_outstanding_account_id
        return super(
            AccountPayment, self - pay_to_update
        )._compute_outstanding_account_id()

    @api.depends("payment_method_line_id")
    def _compute_use_electronic_payment_method(self):
        super(AccountPayment, self)._compute_use_electronic_payment_method()
        for payment in self:
            # Get a list of all electronic payment method codes.
            # These codes are comprised of 'electronic' and the providers of each payment provider.
            if payment.payment_method_code in ("eftpos_amex", "eftpos_visa_mastercard"):
                payment.use_electronic_payment_method = True

    def _get_valid_liquidity_accounts(self):
        result = super()._get_valid_liquidity_accounts()
        return result + (
            self.partner_id
            and self.partner_id.with_company(
                self.company_id
            ).payment_outstanding_account_id
            or self.env["account.account"]
        )

    def _get_valid_payment_account_types(self):
        res = super()._get_valid_payment_account_types()
        if self.partner_id and (
            self.partner_id.with_company(self.company_id).payment_account_id
            or self.company_id.payment_account_id
        ):
            res.append(
                self.partner_id.with_company(
                    self.company_id
                ).payment_account_id.account_type
                if self.partner_id.with_company(self.company_id).payment_account_id
                else self.company_id.payment_account_id.account_type
            )
        return res

    # def _seek_for_lines(self):
    #     lines = super(AccountPayment, self)._seek_for_lines()
    #     payment_account = self.partner_id.with_company(self.company_id).payment_account_id
    #     if payment_account:
    #         lines[0] += payment_account
    #     return lines

    def _cron_batch_payment(self, payment_method=False, date_to_run=False):
        if not date_to_run:
            date_to_run = fields.Date.today()
        else:
            date_to_run = datetime.strptime(date_to_run, "%d/%m/%Y")
        # today = fields.Date.today()
        domain = [
            ("state", "=", "paid"),
            ("payment_type", "=", "inbound"),
            ("date", "=", date_to_run),
            ("batch_payment_id", "=", False),
        ]
        if not payment_method:
            payment_method = [
                "eftpos_amex",
                "eftpos_visa_mastercard",
                "eftpos",
            ]
        if payment_method:
            domain.append(("payment_method_id.code", "in", payment_method))

        all_today_not_batched = self.search(domain)
        for company in all_today_not_batched.mapped("company_id"):
            all_today_not_batched_company = all_today_not_batched.filtered(
                lambda x: x.company_id == company
            )
            for branch in all_today_not_batched_company.mapped("branch_id"):
                all_today_not_batched_branch = all_today_not_batched_company.filtered(
                    lambda x: x.branch_id == branch
                )
                method_groups = {
                    "eftpos_amex": ["eftpos_amex"],
                    "eftpos_visa_mastercard": ["eftpos", "eftpos_visa_mastercard"],
                }

                for group_name, method_codes in method_groups.items():
                    group_payments = all_today_not_batched_branch.filtered(
                        lambda p: p.payment_method_id.code in method_codes
                    )

                    # group is_downpayment
                    dp_payments = group_payments.filtered(lambda p: p.is_downpayment)
                    sch_payments = group_payments.filtered(
                        lambda p: not p.is_downpayment
                    )

                    if dp_payments:
                        dp_payments.create_batch_payment()
                    if sch_payments:
                        sch_payments.create_batch_payment()

    @api.depends(
        "move_id.line_ids.amount_residual",
        "move_id.line_ids.amount_residual_currency",
        "move_id.line_ids.account_id",
        "state",
    )
    def _compute_reconciliation_status(self):
        """Compute the field indicating if the payments are already reconciled with something.
        This field is used for display purpose (e.g. display the 'reconcile' button redirecting to the reconciliation
        widget).
        """
        res = super(AccountPayment, self)._compute_reconciliation_status()
        for pay in self:
            liquidity_lines, counterpart_lines, writeoff_lines = pay._seek_for_lines()

            if not pay.outstanding_account_id:
                pay.is_reconciled = False
                pay.is_matched = pay.state == "paid"
            elif not pay.currency_id or not pay.id or not pay.move_id:
                pay.is_reconciled = False
                pay.is_matched = False
            elif pay.currency_id.is_zero(pay.amount):
                pay.is_reconciled = True
                pay.is_matched = True
            else:
                residual_field = (
                    "amount_residual"
                    if pay.currency_id == pay.company_id.currency_id
                    else "amount_residual_currency"
                )
                if (
                    pay.journal_id.default_account_id
                    and pay.journal_id.default_account_id in liquidity_lines.account_id
                ):
                    # Allow user managing payments without any statement lines by using the bank account directly.
                    # In that case, the user manages transactions only using the register payment wizard.
                    pay.is_matched = True
                else:
                    pay.is_matched = pay.currency_id.is_zero(
                        sum(liquidity_lines.mapped(residual_field))
                    )

                reconcile_lines = (counterpart_lines + writeoff_lines).filtered(
                    lambda line: line.account_id.reconcile
                )
                if reconcile_lines:
                    pay.is_reconciled = pay.currency_id.is_zero(
                        sum(reconcile_lines.mapped(residual_field))
                    )
                else:
                    pay.is_reconciled = False
        return res

    @api.depends("invoice_ids.payment_state", "move_id.line_ids.amount_residual")
    def _compute_state(self):
        for payment in self:
            if not payment.state:
                payment.state = "draft"
            # in_process --> paid
            if (move := payment.move_id) and payment.state in ("paid", "in_process"):
                liquidity, _counterpart, _writeoff = payment._seek_for_lines()
                payment.state = (
                    "paid"
                    if move.company_currency_id.is_zero(
                        sum(liquidity.mapped("amount_residual"))
                    )
                    or not (True in liquidity.account_id.mapped("reconcile"))
                    else "in_process"  # overwrite here for payment have 2 liquidity account
                )
            if (
                payment.state == "in_process"
                and payment.invoice_ids
                and all(
                    invoice.payment_state == "paid" for invoice in payment.invoice_ids
                )
            ):
                payment.state = "paid"

    def action_reverse_and_reconcile(self):
        self.ensure_one()
        copy_payment = self.copy(
            {
                "payment_type": "outbound"
                if self.payment_type == "inbound"
                else "inbound",
            }
        )
        copy_payment.action_post()
        # reconcile the 2 payment together
        for account_id in self.move_id.line_ids.account_id.ids:
            (self.move_id.line_ids + copy_payment.move_id.line_ids).filtered(
                lambda l: l.account_id.id == account_id
            ).reconcile()
        return True


class AccountPaymentMethod(models.Model):
    _inherit = "account.payment.method"

    @api.model
    def _get_payment_method_information(self):
        res = super(AccountPaymentMethod, self)._get_payment_method_information()
        res["eftpos_visa_mastercard"] = {
            "mode": "unique",
            "type": ("bank",),
        }
        res["eftpos_amex"] = {
            "mode": "unique",
            "type": ("bank",),
        }
        return res
