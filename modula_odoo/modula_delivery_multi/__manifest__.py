# -*- coding: utf-8 -*-
{
    'name': 'Multiple Delivery Charges',
    'version': '********.0',
    'category': 'Inventory/Delivery',
    'summary': 'Enable multiple delivery charges per sale order based on product routing',
    'description': """
Multiple Delivery Charges
=========================

This module enables multiple delivery charges in a single sale order based on 
product routing (MTO vs Stock) with automatic rate calculation.

Key Features:
* Automatic product grouping by MTO/Stock routing
* Multiple delivery charges per order
* Integration with postcode-based pricing
* Automatic rate calculation for each delivery group
* Seamless integration with existing delivery system

Business Use Case:
* MTO products: Apply special handling delivery charge
* In-Stock products: Apply standard shipping charge
* Result: 2 separate deliveries with 2 separate delivery charges in one order

Technical Features:
* Group-based rate calculation using existing carrier logic
* Integration with modula_delivery postcode pricing
* Temporary order technique for accurate rate calculation
* Maintains data integrity and proper carrier relationships
    """,
    'author': 'Modula Team',
    'website': 'https://www.modula.com',
    'depends': [
        'delivery',           # Core Odoo delivery functionality
        'sale_stock',        # For MTO/stock routing detection
        'modula_delivery'    # For postcode pricing integration
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/delivery_group_views.xml',
        'views/sale_order_views.xml',
        'wizard/choose_multi_delivery_carrier_views.xml',
    ],
    'demo': [],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}
