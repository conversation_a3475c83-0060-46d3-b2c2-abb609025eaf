# -*- coding: utf-8 -*-

from odoo import models, api, _
from odoo.exceptions import UserError


class DeliveryCarrier(models.Model):
    _inherit = 'delivery.carrier'

    def _apply_taxes_to_price(self, price, order):
        """Apply taxes to delivery price following modula_delivery pattern"""
        if not self.product_id or not self.product_id.taxes_id:
            return price

        price_inc_tax = self.product_id.taxes_id.compute_all(
            price,
            order.company_id.currency_id,
            1,
            self.product_id,
            order.partner_id,
        )["total_included"]

        return price_inc_tax

    def rate_shipment_for_lines(self, order, order_lines):
        """Calculate delivery rate for specific order lines only"""
        self.ensure_one()

        if not order_lines:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No order lines provided for rate calculation'),
            }

        # Use the existing order directly - no temporary order creation needed
        try:
            # Calculate rate using existing logic based on delivery type
            if self.delivery_type == 'postcode':
                result = self.postcode_rate_shipment_for_lines(order, order_lines)
            elif self.delivery_type == 'fixed':
                result = self._calculate_fixed_rate_for_lines(order, order_lines)
            elif self.delivery_type == 'base_on_rule':
                result = self._calculate_rule_based_rate_for_lines(order, order_lines)
            else:
                # For other delivery types, use standard rate calculation
                # but calculate based on the specific lines weight/value
                result = self._calculate_standard_rate_for_lines(order, order_lines)

            return result

        except Exception as e:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('Error calculating delivery rate: %s') % str(e),
            }

    def _calculate_fixed_rate_for_lines(self, order, order_lines):
        """Calculate fixed rate for specific lines"""
        price_inc_tax = self._apply_taxes_to_price(self.fixed_price, order)
        return {
            'success': True,
            'price': price_inc_tax,
            'error_message': False,
        }

    def _calculate_rule_based_rate_for_lines(self, order, order_lines):
        """Calculate rule-based rate for specific lines"""
        # Calculate total weight and value for the specific lines
        total_weight = sum(line.product_qty * (line.product_id.weight or 0.0) for line in order_lines)
        total_value = sum(line.price_subtotal for line in order_lines)

        # Use existing price rule logic but with calculated totals
        try:
            # Find applicable price rule based on weight/value
            price_rule = self.env['delivery.price.rule'].search([
                ('carrier_id', '=', self.id),
                '|', ('max_value', '=', 0), ('max_value', '>=', total_value),
                '|', ('max_weight', '=', 0), ('max_weight', '>=', total_weight),
            ], order='sequence', limit=1)

            if price_rule:
                price = price_rule.list_base_price
                if price_rule.variable_factor:
                    if price_rule.variable == 'weight':
                        price += total_weight * price_rule.variable_factor
                    elif price_rule.variable == 'volume':
                        # Calculate volume if needed
                        total_volume = sum(line.product_qty * (line.product_id.volume or 0.0) for line in order_lines)
                        price += total_volume * price_rule.variable_factor
                    elif price_rule.variable == 'wv':
                        # Weight * Volume
                        total_volume = sum(line.product_qty * (line.product_id.volume or 0.0) for line in order_lines)
                        price += total_weight * total_volume * price_rule.variable_factor
                    elif price_rule.variable == 'price':
                        price += total_value * price_rule.variable_factor

                price_inc_tax = self._apply_taxes_to_price(price, order)
                return {
                    'success': True,
                    'price': price_inc_tax,
                    'error_message': False,
                }
            else:
                return {
                    'success': False,
                    'price': 0.0,
                    'error_message': self.env._('No applicable price rule found'),
                    'warning_message': False
                }
        except Exception as e:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('Error in rule-based calculation: %s') % str(e),
                'warning_message': False
            }

    def _calculate_standard_rate_for_lines(self, order, order_lines):
        """Calculate standard rate for other delivery types"""
        # For other delivery types, use fixed price as fallback
        if self.fixed_price > 0:
            price_inc_tax = self._apply_taxes_to_price(self.fixed_price, order)
            return {
                'success': True,
                'price': price_inc_tax,
                'error_message': False,
                'warning_message': self.env._('Using fixed price for delivery type: %s') % self.delivery_type
            }
        else:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No pricing configured for delivery type: %s') % self.delivery_type,
            }

    def postcode_rate_shipment_for_lines(self, order, order_lines):
        """Postcode-based rate calculation for specific lines"""
        self.ensure_one()
        
        if self.delivery_type != 'postcode':
            return self.rate_shipment_for_lines(order, order_lines)
        
        # Get shipping address details
        shipping_partner = order.partner_shipping_id
        if not shipping_partner:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No shipping address found'),
            }
        
        postcode = shipping_partner.zip
        country = shipping_partner.country_id
        state = shipping_partner.state_id
        city = shipping_partner.city
        
        if not postcode:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('No postcode found in shipping address'),
            }
        
        # Use existing postcode pricing logic from modula_delivery
        try:
            price = self.env['delivery.postcode.pricelist'].find_delivery_price(
                self, postcode, country, state, city
            )
            
            if price is not False:
                price_inc_tax = self._apply_taxes_to_price(price, order)
                return {
                    'success': True,
                    'price': price_inc_tax,
                    'error_message': False,
                }
            else:
                # Use fallback pricing
                if hasattr(self, 'postcode_fixed_price') and self.postcode_fixed_price > 0:
                    product_names = order_lines.mapped('product_id.name')
                    price_inc_tax = self._apply_taxes_to_price(self.postcode_fixed_price, order)
                    return {
                        'success': True,
                        'price': price_inc_tax,
                        'error_message': False,
                        'warning_message': self.env._(
                            'Using postcode fixed price for products: %s'
                        ) % ', '.join(product_names[:3]) + ('...' if len(product_names) > 3 else '')
                    }
                elif self.fixed_price > 0:
                    price_inc_tax = self._apply_taxes_to_price(self.fixed_price, order)
                    return {
                        'success': True,
                        'price': price_inc_tax,
                        'error_message': False,
                        'warning_message': self.env._('Using carrier fixed price as fallback')
                    }
                else:
                    return {
                        'success': False,
                        'price': 0.0,
                        'error_message': self.env._('No delivery price found for postcode %s') % postcode,
                    }
                    
        except Exception as e:
            return {
                'success': False,
                'price': 0.0,
                'error_message': self.env._('Error in postcode rate calculation: %s') % str(e),
            }
    
    def available_carriers_for_order_lines(self, order, order_lines):
        """Get available carriers for specific order lines"""
        # Get all carriers that could potentially handle this delivery
        available_carriers = self.search([
            ('delivery_type', 'in', ['fixed', 'base_on_rule', 'postcode'])
        ])
        
        # Filter carriers based on order and lines
        valid_carriers = self.env['delivery.carrier']
        
        for carrier in available_carriers:
            try:
                # Test if carrier can handle this order/lines combination
                result = carrier.rate_shipment_for_lines(order, order_lines)
                if result.get('success'):
                    valid_carriers |= carrier
            except Exception:
                # Skip carriers that fail
                continue
        
        return valid_carriers
    
    @api.model
    def get_carriers_for_delivery_group(self, order, group_type, order_lines):
        """Get suitable carriers for a specific delivery group"""
        # Get available carriers
        carriers = self.available_carriers_for_order_lines(order, order_lines)
        
        # Could add group-type specific filtering here
        # For example, different carriers for MTO vs Stock
        if group_type == 'mto':
            # Could filter for carriers that support MTO
            pass
        elif group_type == 'stock':
            # Could filter for carriers that support stock items
            pass
        
        return carriers
