<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="group_chatter_audit_log" model="res.groups">
        <field name="name">Show Audit Log tab for Chatter</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('modula_separated_activity_audit_log.group_chatter_audit_log'))]"/>
    </record>
</odoo>
