# Part of Odoo. See LICENSE file for full copyright and licensing details.

import logging

from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError

_logger = logging.getLogger(__name__)


class DeliveryCarrier(models.Model):
    _inherit = "delivery.carrier"

    # Add a new delivery type for postcode-based pricing
    delivery_type = fields.Selection(
        selection_add=[("postcode", "Postcode-Based")],
        ondelete={"postcode": "set default"},
    )

    postcode_pricelist_ids = fields.One2many(
        "delivery.postcode.pricelist",
        "delivery_carrier_id",
        string="Postcode Pricelists",
        help="Postcode-based pricing rules for this carrier",
    )

    postcode_pricelist_count = fields.Integer(
        string="Pricelist Count", compute="_compute_postcode_pricelist_count"
    )

    postcode_fixed_price = fields.Float(
        string="Postcode Fixed Price",
        help="Fixed price to use when no postcode pricelist matches the delivery address. "
        "This is the only fallback price for postcode-based delivery.",
        digits="Product Price",
    )

    @api.depends("postcode_pricelist_ids")
    def _compute_postcode_pricelist_count(self):
        """Compute the number of postcode pricelist entries."""
        for carrier in self:
            carrier.postcode_pricelist_count = len(carrier.postcode_pricelist_ids)

    # -------------------------------- #
    # Override standard delivery methods #
    # -------------------------------- #

    def _is_available_for_order(self, order):
        """
        Override to check if postcode-based delivery is available for the order.
        This method is called by Odoo to determine if this carrier should be shown.
        """
        # First check the standard availability
        if not super()._is_available_for_order(order):
            return False

        # For postcode delivery, check if we have a valid postcode and matching pricelist
        if self.delivery_type == "postcode":
            if not order.partner_shipping_id or not order.partner_shipping_id.zip:
                return False

            # Check if we have any matching pricelist entries
            postcode = order.partner_shipping_id.zip
            country = order.partner_shipping_id.country_id
            state = order.partner_shipping_id.state_id
            city = order.partner_shipping_id.city

            price = self.env["delivery.postcode.pricelist"].find_delivery_price(
                self, postcode, country, state, city
            )
            # Available if we have a matching pricelist or a postcode fixed price
            return price is not False or self.postcode_fixed_price > 0

        return True

    def postcode_rate_shipment(self, order):
        """
        Calculate shipping rate based on postcode pricelist.
        This method is called when delivery_type is 'postcode'.

        :param order: sale.order record
        :return: dict with success, price, error_message, warning_message
        """
        self.ensure_one()

        # Address and availability checks are handled in _is_available_for_order
        # This method is only called if the carrier is available for the order

        postcode = order.partner_shipping_id.zip
        country = order.partner_shipping_id.country_id
        state = order.partner_shipping_id.state_id
        city = order.partner_shipping_id.city
        company = self.company_id or order.company_id or self.env.company

        # Look up price in postcode pricelist
        price = self.env["delivery.postcode.pricelist"].find_delivery_price(
            self, postcode, country, state, city
        )
        price_inc_tax =  self.product_id.taxes_id.compute_all(
            price,
            company.currency_id,
            1,
            self.product_id,
            self.env.user.partner_id,
        )["total_included"]

        if price is not False:
            return {
                "success": True,
                "price": price_inc_tax,
                "error_message": False,
                "warning_message": False,
            }
        else:
            # Use postcode_fixed_price as the only fallback
            if self.postcode_fixed_price > 0:
                price_inc_tax = self.product_id.taxes_id.compute_all(
                    self.postcode_fixed_price,
                    company.currency_id,
                    1,
                    self.product_id,
                    self.env.user.partner_id,
                )["total_included"]

                return {
                    "success": True,
                    "price": price_inc_tax,
                    "error_message": False,
                    "warning_message": _(
                        "No specific postcode price found for %s. Using postcode fixed price %.2f."
                    )
                    % (postcode, price_inc_tax),
                }
            else:
                return {
                    "success": False,
                    "price": 0.0,
                    "error_message": _(
                        "No delivery price found for postcode %s and no postcode fixed price configured."
                    )
                    % postcode,
                    "warning_message": False,
                }

    # -------------------------------- #
    # Optional methods for complete shipping provider support #
    # -------------------------------- #

    def postcode_send_shipping(self, pickings):
        """
        Send the package to the service provider.
        For postcode-based delivery, this calculates the actual delivery cost
        based on the shipping address postcode.

        :param pickings: A recordset of pickings
        :return list: A list of dictionaries (one per picking) containing::
                     { 'exact_price': price,
                       'tracking_number': number }
        """
        self.ensure_one()
        res = []
        for picking in pickings:
            # Calculate exact price based on postcode
            if picking.partner_id and picking.partner_id.zip:
                postcode = picking.partner_id.zip
                country = picking.partner_id.country_id
                state = picking.partner_id.state_id
                city = picking.partner_id.city

                price = self.env["delivery.postcode.pricelist"].find_delivery_price(
                    self, postcode, country, state, city
                )

                price_inc_tax = self.product_id.taxes_id.compute_all(
                    price,
                    self.env.company.currency_id,
                    1,
                    self.product_id,
                    self.env.user.partner_id,
                )["total_included"]

                if price is not False:
                    exact_price = price_inc_tax
                else:
                    price_inc_tax = self.product_id.taxes_id.compute_all(
                        self.postcode_fixed_price,
                        company.currency_id,
                        1,
                        self.product_id,
                        self.env.user.partner_id,
                    )["total_included"]
                    # Fallback to postcode_fixed_price only
                    exact_price = price_inc_tax or 0.0
                    _logger.warning(
                        "No postcode pricelist found for %s, using postcode fixed price %.2f",
                        postcode,
                        exact_price,
                    )
            else:
                # Use postcode_fixed_price as fallback when no postcode available
                exact_price = self.product_id.taxes_id.compute_all(
                        self.postcode_fixed_price,
                        company.currency_id,
                        1,
                        self.product_id,
                        self.env.user.partner_id,
                    )["total_included"] or 0.0
                _logger.warning(
                    "No postcode available for picking %s, using postcode fixed price %.2f",
                    picking.name,
                    exact_price,
                )

            res.append(
                {
                    "exact_price": exact_price,
                    "tracking_number": False,  # Basic postcode delivery doesn't provide tracking
                }
            )
        return res

    def postcode_get_tracking_link(self, picking):
        """
        Get tracking link for the shipment.
        For postcode delivery, use the standard tracking URL if configured.

        :param picking: record of stock.picking
        :return str: an URL containing the tracking link or False
        """
        self.ensure_one()
        # Use the standard tracking URL pattern if configured
        if self.tracking_url and picking.carrier_tracking_ref:
            return self.tracking_url.replace(
                "<shipmenttrackingnumber>", picking.carrier_tracking_ref
            )
        return False

    def postcode_cancel_shipment(self, pickings):
        """
        Cancel a shipment.
        For postcode-based delivery, this clears tracking information.

        :param pickings: A recordset of pickings
        """
        self.ensure_one()
        # Clear tracking information for cancelled shipments
        for picking in pickings:
            picking.write(
                {
                    "carrier_tracking_ref": False,
                    "carrier_price": 0.0,
                }
            )
            _logger.info("Cancelled postcode delivery for picking %s", picking.name)

    def _postcode_get_default_custom_package_code(self):
        """
        Get default custom package code for postcode delivery.

        :return str: default package code
        """
        self.ensure_one()
        # Return a default package code for postcode-based delivery
        return "POSTCODE_STD"

    # -------------------------------- #
    # UI Helper Methods #
    # -------------------------------- #

    def action_view_postcode_pricelists(self):
        """Action to view postcode pricelists for this carrier."""
        self.ensure_one()
        return {
            "type": "ir.actions.act_window",
            "name": _("Postcode Pricelists"),
            "res_model": "delivery.postcode.pricelist",
            "view_mode": "list,form",
            "domain": [("delivery_carrier_id", "=", self.id)],
            "context": {"default_delivery_carrier_id": self.id},
            "target": "current",
        }
