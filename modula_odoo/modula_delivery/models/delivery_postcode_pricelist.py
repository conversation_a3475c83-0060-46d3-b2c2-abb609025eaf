# Part of Odoo. See LICENSE file for full copyright and licensing details.

import re
import logging
from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError

_logger = logging.getLogger(__name__)


class DeliveryPostcodePricelist(models.Model):
    _name = 'delivery.postcode.pricelist'
    _description = 'Delivery Postcode Pricelist'
    _order = 'delivery_carrier_id, match_priority, sequence, postcode_from'
    _rec_name = 'name'

    name = fields.Char(
        string='Pricelist Name',
        compute='_compute_display_name',
        help="Name for this postcode pricelist entry"
    )

    delivery_carrier_id = fields.Many2one(
        'delivery.carrier',
        string='Delivery Carrier',
        required=True,
        ondelete='cascade',
        help="The delivery carrier this pricelist applies to"
    )

    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Priority order for matching. Lower values have higher priority."
    )

    postcode_from = fields.Char(
        string='Postcode From',
        required=True,
        help="Starting postcode or single postcode for exact match"
    )

    postcode_to = fields.Char(
        string='Postcode To',
        help="Ending postcode for range matching. Leave empty for exact postcode match."
    )

    country_id = fields.Many2one(
        'res.country',
        string='Country',
        help="Restrict this pricelist to a specific country"
    )

    state_id = fields.Many2one(
        'res.country.state',
        string='State',
        help="Restrict this pricelist to a specific state"
    )

    city = fields.Char(
        string='City',
        help="Restrict this pricelist to a specific city. Leave empty to apply to all cities."
    )

    price = fields.Float(
        string='Delivery Price',
        required=True,
        digits='Product Price',
        help="Delivery charge for this postcode/range"
    )

    price_inc_tax = fields.Float(
        string='Delivery Price (Inc GST)',
        compute='_compute_price_inc_tax',
        digits='Product Price',
        help="Delivery charge for this postcode/range including tax"
    )

    @api.depends('price', 'delivery_carrier_id')
    def _compute_price_inc_tax(self):
        for record in self:
            if record.delivery_carrier_id.product_id:
                record.price_inc_tax = record.delivery_carrier_id.product_id.taxes_id.compute_all(
                    record.price,
                    record.delivery_carrier_id.currency_id,
                    1,
                    record.delivery_carrier_id.product_id,
                    record.delivery_carrier_id.env.user.partner_id,
                )["total_included"]

    active = fields.Boolean(
        string='Active',
        default=True,
        help="Uncheck to disable this pricelist entry"
    )

    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        related='delivery_carrier_id.currency_id',
        readonly=True
    )

    is_range = fields.Boolean(
        string='Is Range',
        compute='_compute_is_range',
        store=True,
        help="True if this entry defines a postcode range"
    )

    match_priority = fields.Integer(
        string='Match Priority',
        compute='_compute_match_priority',
        store=True,
        help="Priority for matching: 1=Exact, 2=Wildcard, 3=Range"
    )

    @api.depends('postcode_to')
    def _compute_is_range(self):
        """Compute whether this is a range or single postcode entry."""
        for record in self:
            record.is_range = bool(record.postcode_to)

    @api.depends('postcode_from', 'postcode_to')
    def _compute_match_priority(self):
        """
        Compute match priority for ordering:
        1 = Exact match (no wildcard, no range)
        2 = Wildcard match (contains *)
        3 = Range match (has postcode_to)
        """
        for record in self:
            if record.postcode_to:  # Range match
                record.match_priority = 3
            elif record.postcode_from and '*' in record.postcode_from:  # Wildcard
                record.match_priority = 2
            else:  # Exact match
                record.match_priority = 1

    @api.constrains('postcode_from', 'postcode_to')
    def _check_postcode_format(self):
        """Validate postcode format and range logic."""
        for record in self:
            if not record.postcode_from:
                raise ValidationError(_("Postcode From is required."))

            # Clean and validate postcodes
            postcode_from = self._clean_postcode(record.postcode_from)
            if record.postcode_to:
                postcode_to = self._clean_postcode(record.postcode_to)

                # For ranges, both postcodes should be numeric for comparison
                if postcode_from.isdigit() and postcode_to.isdigit():
                    if int(postcode_from) >= int(postcode_to):
                        raise ValidationError(
                            _("Postcode From (%s) must be less than Postcode To (%s).")
                            % (record.postcode_from, record.postcode_to)
                        )

    @api.constrains('price')
    def _check_price(self):
        """Validate that price is not negative."""
        for record in self:
            if record.price < 0:
                raise ValidationError(_("Delivery price cannot be negative."))

    @api.model
    def _clean_postcode(self, postcode):
        """Clean postcode by removing spaces and converting to uppercase."""
        if not postcode:
            return ''
        return re.sub(r'\s+', '', postcode.upper())

    def _match_postcode(self, postcode, country=None, state=None, city=None):
        """
        Check if the given postcode matches this pricelist entry.

        :param postcode: The postcode to match
        :param country: Optional country record
        :param state: Optional state record
        :param city: Optional city string
        :return: True if matches, False otherwise
        """
        self.ensure_one()

        if not self.active:
            return False

        # Check country restriction
        if self.country_id and country and self.country_id != country:
            return False

        # Check state restriction
        if self.state_id and state and self.state_id != state:
            return False

        # Check city restriction
        if self.city and city:
            # Case-insensitive city matching
            if self.city.lower().strip() != city.lower().strip():
                return False

        # Clean the input postcode
        clean_postcode = self._clean_postcode(postcode)
        clean_from = self._clean_postcode(self.postcode_from)

        if not clean_postcode or not clean_from:
            return False

        # Exact match for single postcode
        if not self.postcode_to:
            return self._match_single_postcode(clean_postcode, clean_from)

        # Range match
        clean_to = self._clean_postcode(self.postcode_to)
        return self._match_postcode_range(clean_postcode, clean_from, clean_to)

    def _match_single_postcode(self, postcode, pattern):
        """
        Match a single postcode against a pattern.
        Supports wildcards (*) and exact matches.
        """
        # Convert wildcard pattern to regex
        if '*' in pattern:
            regex_pattern = pattern.replace('*', '.*')
            return bool(re.match(f'^{regex_pattern}$', postcode))

        # Exact match
        return postcode == pattern

    def _match_postcode_range(self, postcode, from_code, to_code):
        """
        Match postcode against a range.
        Works with numeric postcodes.
        """
        # For numeric postcodes, do numeric comparison
        if postcode.isdigit() and from_code.isdigit() and to_code.isdigit():
            postcode_num = int(postcode)
            from_num = int(from_code)
            to_num = int(to_code)
            return from_num <= postcode_num <= to_num

        # For alphanumeric postcodes, do string comparison
        return from_code <= postcode <= to_code

    @api.model
    def find_delivery_price(self, carrier, postcode, country=None, state=None, city=None):
        """
        Find the delivery price for a given carrier and postcode.
        Priority order:
        1. Exact postcode matches (ordered by sequence)
        2. Wildcard matches (ordered by sequence)
        3. Range matches (ordered by sequence)

        :param carrier: delivery.carrier record
        :param postcode: The postcode to lookup
        :param country: Optional country record
        :param state: Optional state record
        :param city: Optional city string
        :return: Price (float) or False if not found
        """
        if not carrier or not postcode:
            return False

        # Search for matching pricelist entries
        domain = [
            ('delivery_carrier_id', '=', carrier.id),
            ('active', '=', True)
        ]

        # Add country filter if specified
        if country:
            domain.append(('country_id', 'in', [False, country.id]))

        # Add state filter if specified
        if state:
            domain.append(('state_id', 'in', [False, state.id]))

        # Get all potential matches ordered by sequence
        all_entries = self.search(domain)

        # Separate entries by type for priority matching
        exact_entries = []
        wildcard_entries = []
        range_entries = []

        for entry in all_entries:
            if not entry.postcode_to:  # Single postcode (exact or wildcard)
                clean_from = self._clean_postcode(entry.postcode_from)
                if '*' in clean_from:
                    wildcard_entries.append(entry)
                else:
                    exact_entries.append(entry)
            else:  # Range entries
                range_entries.append(entry)

        # Priority 1: Check exact matches first (ordered by sequence)
        for entry in exact_entries:
            if entry._match_postcode(postcode, country, state, city):
                _logger.info(
                    "Found EXACT delivery price %.2f for postcode %s using carrier %s (entry: %s)",
                    entry.price, postcode, carrier.name, entry.name
                )
                return entry.price

        # Priority 2: Check wildcard matches (ordered by sequence)
        for entry in wildcard_entries:
            if entry._match_postcode(postcode, country, state, city):
                _logger.info(
                    "Found WILDCARD delivery price %.2f for postcode %s using carrier %s (entry: %s)",
                    entry.price, postcode, carrier.name, entry.name
                )
                return entry.price

        # Priority 3: Check range matches (ordered by sequence)
        for entry in range_entries:
            if entry._match_postcode(postcode, country, state, city):
                _logger.info(
                    "Found RANGE delivery price %.2f for postcode %s using carrier %s (entry: %s)",
                    entry.price, postcode, carrier.name, entry.name
                )
                return entry.price
        # Check if carrier has a postcode fixed price as fallback
        if carrier.postcode_fixed_price > 0:
            _logger.info(
                "Using postcode fixed price %.2f for postcode %s with carrier %s",
                carrier.postcode_fixed_price, postcode, carrier.name
            )
            return carrier.postcode_fixed_price
        _logger.warning(
            "No delivery price found for postcode %s with carrier %s",
            postcode, carrier.name
        )
        return False

    @api.depends('city', 'postcode_from', 'postcode_to')
    def _compute_display_name(self):
        for record in self:
            if record.city:
                record.display_name = f"{record.city} {record.postcode_from}{f'-{record.postcode_to}' if record.postcode_to else ''}"
                record.name = f"{record.city} {record.postcode_from}{f'-{record.postcode_to}' if record.postcode_to else ''}"

            else:
                record.display_name = f"{record.name} {record.postcode_from}{f'-{record.postcode_to}' if record.postcode_to else ''}"
                record.name = f"{record.name} {record.postcode_from}{f'-{record.postcode_to}' if record.postcode_to else ''}"