# -*- coding: utf-8 -*-
{
    'name': "Modula Sale Employee Selection",
    'summary': """
        Module for employee selection in sales processes
    """,
    'description': """
        This module extends sales functionality to allow employee selection
        and management within the sales process.
    """,
    'author': "ITMS",
    'website': "https://www.itms.com",
    
    'category': 'Sales',
    'version': '********.0',
    'license': 'LGPL-3',
    
    'depends': [
        'web',
        'hr',
        'sale_management',
        'stock',
    ],
    'assets': {
        'web.assets_backend': [
            'modula_sale_employee_selection/static/**/*',
        ],
    },
    'data': [
        'views/stock_picking_views.xml',
    ],
    'application': False,
    'installable': True,
    'auto_install': False,
}

