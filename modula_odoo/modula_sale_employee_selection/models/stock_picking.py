# -*- coding: utf-8 -*-
from odoo.http import request
from odoo import api, fields, models

from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = "stock.picking"

    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
    )

    def button_validate(self):
        if not request.session.get("session_owner", False):
            raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
        else:
            self.employee_id = request.session.get("session_owner", False)
            request.session["session_owner"] = False
        return super(StockPicking, self).button_validate()

    @api.model
    def _get_view(self, view_id, view_type, **options):
        arch, view = super()._get_view(view_id, view_type, **options)
        if arch.xpath("//button[@name='button_validate']"):
            arch.xpath("//button[@name='button_validate']")[0].set("groups", "base.group_no_one")
        return arch, view

    def create(self, vals):
        res = super(StockPicking, self).create(vals)
        return res