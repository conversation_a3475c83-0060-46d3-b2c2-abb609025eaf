<?xml version ="1.0" encoding="utf-8"?>
<odoo>
	<record id="view_order_display_stock_form_inherit" model="ir.ui.view">
		<field name="name">sale.order.display.stock.form.inherit</field>
		<field name="model">sale.order</field>
		<field name="inherit_id" ref="modula_sale.view_order_form_inherit"/>
		<field name="arch" type="xml">
            <field name="location_id" position="attributes">
                <attribute name="domain">[('is_display_stock', '=', False)]</attribute>
            </field>
			<xpath expr="//field[@name='order_line']/list//field[@name='product_uom']" position="before">
                <field name="stock_location_quant_manager_id" column_invisible="1"/>
            </xpath>
			<xpath expr="//field[@name='order_line']/list" position="inside">
                <field name="delivery_d_run" />
            </xpath>
			<xpath expr="//field[@name='order_line']/list//button[@name='action_open_available_stock']" position="attributes">
				<attribute name="invisible">not stock_location_quant_manager_id</attribute>
			</xpath>
 		</field>
	</record>
</odoo>
