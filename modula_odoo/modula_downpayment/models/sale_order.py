# -*- coding: utf-8 -*-
from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    downpayment_ids = fields.One2many(
        "account.payment", "sale_order_id", string="Down Payments"
    )
    downpayment_count = fields.Integer(
        compute="_compute_downpayment_count", string="Down Payment Count"
    )
    remaining_amount = fields.Monetary(
        string="Remaining Amount",
        compute="_compute_remaining_amount",
        store=True,
        precompute=True,
    )
    minimum_deposit_total = fields.Monetary(
        string="Minimum Deposit Total",
        compute="_compute_minimum_deposit_total",
    )

    @api.depends("order_line.minimum_deposit")
    def _compute_minimum_deposit_total(self):
        for order in self:
            order.minimum_deposit_total = sum(
                order.order_line.mapped("minimum_deposit")
            )

    @api.depends(
        "amount_total",
        "downpayment_ids.amount_signed",
        "downpayment_ids.state",
        "downpayment_ids.move_id.reversal_move_ids",
    )
    def _compute_remaining_amount(self):
        for order in self:
            remaining_amount = order.amount_total - sum(
                order.downpayment_ids.filtered(
                    lambda x: x.state in ["paid", "in_process"]
                ).mapped("amount_signed")
            )
            order.remaining_amount = remaining_amount

    @api.depends("downpayment_ids")
    def _compute_downpayment_count(self):
        for order in self:
            order.downpayment_count = len(order.downpayment_ids)

    def button_create_down_payment(self):
        self.ensure_one()
        return {
            "name": "Create Down Payment",
            "type": "ir.actions.act_window",
            "res_model": "account.payment",
            "view_mode": "form",
            "views": [[self.env.ref("account.view_account_payment_form").id, "form"]],
            "domain": [("sale_order_id", "=", self.id)],
            "target": "current",
            "context": {
                "default_sale_order_id": self.id,
                "default_partner_id": self.partner_id.id,
                "default_payment_type": "inbound",
                "default_partner_type": "customer",
                "default_is_downpayment": True,
                "default_move_journal_types": ("bank", "cash"),
            },
        }

    def button_view_down_payment(self):
        self.ensure_one()
        return {
            "name": "Create Down Payment",
            "type": "ir.actions.act_window",
            "res_model": "account.payment",
            "view_mode": "list,form",
            "view_type": "list",
            "views": [
                [self.env.ref("account.view_account_payment_tree").id, "list"],
                [self.env.ref("account.view_account_payment_form").id, "form"],
            ],
            "domain": [("sale_order_id", "=", self.id)],
            "target": "current",
            "context": {
                "default_sale_order_id": self.id,
                "default_partner_id": self.partner_id.id,
                "default_payment_type": "inbound",
                "default_partner_type": "customer",
                "default_is_downpayment": True,
                "default_move_journal_types": ("bank", "cash"),
            },
        }
